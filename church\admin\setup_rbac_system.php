<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$message = '';
$error = '';

// Handle RBAC system setup
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'setup_rbac') {
            // Read and execute the SQL setup file
            $sql_file = __DIR__ . '/setup_role_system.sql';
            if (!file_exists($sql_file)) {
                throw new Exception('SQL setup file not found');
            }
            
            $sql_content = file_get_contents($sql_file);
            $statements = explode(';', $sql_content);
            
            $pdo->beginTransaction();
            
            $executed_count = 0;
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^--/', $statement)) {
                    try {
                        $pdo->exec($statement);
                        $executed_count++;
                    } catch (PDOException $e) {
                        // Log but continue - some statements might fail if tables already exist
                        error_log("RBAC Setup - Statement failed: " . $e->getMessage());
                    }
                }
            }
            
            // Assign super_admin role to current admin
            $current_admin_id = $_SESSION['admin_id'];
            
            // Get super_admin role ID
            $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE role_name = 'super_admin'");
            $stmt->execute();
            $super_admin_role_id = $stmt->fetchColumn();
            
            if ($super_admin_role_id) {
                // Assign super_admin role to current user
                $stmt = $pdo->prepare("
                    INSERT INTO user_role_assignments (user_id, role_id, assigned_by) 
                    VALUES (?, ?, ?)
                    ON DUPLICATE KEY UPDATE is_active = 1
                ");
                $stmt->execute([$current_admin_id, $super_admin_role_id, $current_admin_id]);
            }
            
            $pdo->commit();
            $message = "RBAC system setup completed successfully! Executed {$executed_count} SQL statements. You now have Super Admin access.";

            // If setup was required, redirect to Super Admin Dashboard after a delay
            if (isset($_GET['setup_required'])) {
                echo "<script>
                    setTimeout(function() {
                        window.location.href = 'super_admin_dashboard.php';
                    }, 3000);
                </script>";
            }
            
        } elseif ($_POST['action'] === 'assign_role') {
            $user_id = $_POST['user_id'] ?? '';
            $role_id = $_POST['role_id'] ?? '';
            $expires_at = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
            
            if (empty($user_id) || empty($role_id)) {
                throw new Exception('User ID and Role ID are required');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO user_role_assignments (user_id, role_id, assigned_by, expires_at) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE 
                assigned_by = VALUES(assigned_by),
                expires_at = VALUES(expires_at),
                is_active = 1
            ");
            $stmt->execute([$user_id, $role_id, $_SESSION['admin_id'], $expires_at]);
            
            $message = "Role assigned successfully!";
            
        } elseif ($_POST['action'] === 'assign_event') {
            $user_id = $_POST['user_id'] ?? '';
            $event_id = $_POST['event_id'] ?? '';
            $role_type = $_POST['role_type'] ?? '';
            
            if (empty($user_id) || empty($event_id) || empty($role_type)) {
                throw new Exception('All fields are required');
            }
            
            $stmt = $pdo->prepare("
                INSERT INTO event_assignments (user_id, event_id, role_type, assigned_by) 
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE is_active = 1
            ");
            $stmt->execute([$user_id, $event_id, $role_type, $_SESSION['admin_id']]);
            
            $message = "Event assignment completed successfully!";
            
        } elseif ($_POST['action'] === 'assign_session') {
            $user_id = $_POST['user_id'] ?? '';
            $session_id = $_POST['session_id'] ?? '';

            if (empty($user_id) || empty($session_id)) {
                throw new Exception('User ID and Session ID are required');
            }

            $stmt = $pdo->prepare("
                INSERT INTO session_assignments (user_id, session_id, assigned_by)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE is_active = 1
            ");
            $stmt->execute([$user_id, $session_id, $_SESSION['admin_id']]);

            $message = "Session assignment completed successfully!";

        } elseif ($_POST['action'] === 'delete_role_assignment') {
            $assignment_id = $_POST['assignment_id'] ?? '';

            if (empty($assignment_id)) {
                throw new Exception('Assignment ID is required');
            }

            $stmt = $pdo->prepare("UPDATE user_role_assignments SET is_active = 0 WHERE id = ?");
            $stmt->execute([$assignment_id]);

            $message = "Role assignment removed successfully!";

        } elseif ($_POST['action'] === 'delete_session_assignment') {
            $assignment_id = $_POST['assignment_id'] ?? '';

            if (empty($assignment_id)) {
                throw new Exception('Assignment ID is required');
            }

            $stmt = $pdo->prepare("UPDATE session_assignments SET is_active = 0 WHERE id = ?");
            $stmt->execute([$assignment_id]);

            $message = "Session assignment removed successfully!";

        } elseif ($_POST['action'] === 'extend_role_assignment') {
            $assignment_id = $_POST['assignment_id'] ?? '';
            $new_expiry = $_POST['new_expiry'] ?? '';

            if (empty($assignment_id)) {
                throw new Exception('Assignment ID is required');
            }

            $stmt = $pdo->prepare("UPDATE user_role_assignments SET expires_at = ? WHERE id = ?");
            $stmt->execute([$new_expiry ?: null, $assignment_id]);

            $message = $new_expiry ? "Role assignment extended successfully!" : "Role assignment set to permanent!";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Check if RBAC system is already set up
$rbac_setup = false;
try {
    $stmt = $pdo->query("SELECT COUNT(*) FROM user_roles");
    $role_count = $stmt->fetchColumn();
    $rbac_setup = ($role_count > 0);
} catch (PDOException $e) {
    $rbac_setup = false;
}

// Get all admins for role assignment
$admins = [];
try {
    $stmt = $pdo->query("SELECT id, username, email FROM admins ORDER BY username");
    $admins = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist yet
}

// Get all roles
$roles = [];
if ($rbac_setup) {
    $stmt = $pdo->query("SELECT * FROM user_roles ORDER BY hierarchy_level ASC");
    $roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
}

// Get all events
$events = [];
try {
    $stmt = $pdo->query("SELECT id, title, event_date FROM events WHERE is_active = 1 ORDER BY event_date DESC");
    $events = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Table might not exist yet
}

// Get all sessions
$sessions = [];
try {
    $stmt = $pdo->query("
        SELECT es.id, es.session_title, es.start_datetime, e.title as event_title
        FROM event_sessions es
        JOIN events e ON es.event_id = e.id
        WHERE es.status = 'active' AND e.is_active = 1
        ORDER BY es.start_datetime ASC
    ");
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    // Tables might not exist yet
}

// Get current role assignments
$current_assignments = [];
if ($rbac_setup) {
    try {
        $stmt = $pdo->query("
            SELECT ura.id, ura.user_id, ura.role_id, ura.assigned_at, ura.expires_at,
                   ur.role_display_name, ur.role_name, a.username, a.email
            FROM user_role_assignments ura
            JOIN user_roles ur ON ura.role_id = ur.id
            JOIN admins a ON ura.user_id = a.id
            WHERE ura.is_active = 1
            ORDER BY a.username, ur.hierarchy_level
        ");
        $current_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        // Handle error
    }
}

// Page title and header info
$page_title = 'RBAC System Setup';
$page_header = 'Role-Based Access Control Setup';
$page_description = 'Configure hierarchical dashboard roles and permissions';

// Include header
include 'includes/header.php';
?>

<style>
.setup-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}
.setup-card.completed {
    border-color: #28a745;
    background-color: #f8fff9;
}
.role-hierarchy {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}
.assignment-card {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
    position: relative;
}
.assignment-card:hover {
    background-color: #ffffff;
    border-color: #007bff;
    box-shadow: 0 2px 8px rgba(0,123,255,0.1);
}
.btn-group-vertical .btn {
    margin-bottom: 2px;
}
.btn-group-vertical .btn:last-child {
    margin-bottom: 0;
}
.assignment-card .btn-sm {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}
.modal-body .alert {
    margin-bottom: 1rem;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h2><i class="bi bi-shield-check"></i> RBAC System Setup</h2>
                <p class="text-muted mb-0">Configure Role-Based Access Control for Multi-Tier Dashboard Architecture</p>
            </div>
            <div>
                <a href="event_attendance.php" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-left"></i> Back to Events
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Setup Required Message -->
<?php if (isset($_GET['setup_required'])): ?>
    <div class="alert alert-warning alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i>
        <strong>RBAC System Setup Required!</strong>
        <p class="mb-0">The Role-Based Access Control system must be initialized before you can access the Super Admin Dashboard. Please click the "Initialize RBAC System" button below.</p>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- RBAC System Status -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card setup-card <?php echo $rbac_setup ? 'completed' : ''; ?>">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-<?php echo $rbac_setup ? 'check-circle text-success' : 'exclamation-triangle text-warning'; ?>"></i>
                    RBAC System Status
                </h5>
            </div>
            <div class="card-body">
                <?php if ($rbac_setup): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i>
                        <strong>RBAC System is Active</strong>
                        <p class="mb-0">The role-based access control system is set up and running. You can now assign roles and manage permissions.</p>
                    </div>
                <?php else: ?>
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>RBAC System Not Set Up</strong>
                        <p class="mb-2">The role-based access control system needs to be initialized. This will create the necessary database tables and default roles.</p>
                        <a href="initialize_rbac_database.php" class="btn btn-warning">
                            <i class="bi bi-gear"></i> Initialize RBAC System
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php if ($rbac_setup): ?>
<!-- Role Hierarchy Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card role-hierarchy">
            <div class="card-body">
                <h5 class="card-title text-white mb-3">
                    <i class="bi bi-diagram-3"></i> Dashboard Hierarchy
                </h5>
                <div class="row text-center">
                    <?php foreach ($roles as $role): ?>
                        <div class="col-md-<?php echo 12 / count($roles); ?>">
                            <div class="mb-2">
                                <i class="bi bi-person-badge fs-2 d-block"></i>
                                <strong><?php echo htmlspecialchars($role['role_display_name']); ?></strong>
                                <br>
                                <small>Level <?php echo $role['hierarchy_level']; ?></small>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                <div class="mt-3 text-center">
                    <small class="text-white-50">
                        <i class="bi bi-info-circle"></i>
                        Lower hierarchy levels have higher privileges. Super Admin (Level 1) can access all dashboards.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Role Assignment -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus"></i> Assign User Roles
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <input type="hidden" name="action" value="assign_role">
                    <div class="mb-3">
                        <label class="form-label">Select User</label>
                        <select class="form-select" name="user_id" required>
                            <option value="">Choose user...</option>
                            <?php foreach ($admins as $admin): ?>
                                <option value="<?php echo $admin['id']; ?>">
                                    <?php echo htmlspecialchars($admin['username']); ?>
                                    <?php if ($admin['email']): ?>
                                        (<?php echo htmlspecialchars($admin['email']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select Role</label>
                        <select class="form-select" name="role_id" required>
                            <option value="">Choose role...</option>
                            <?php foreach ($roles as $role): ?>
                                <option value="<?php echo $role['id']; ?>">
                                    <?php echo htmlspecialchars($role['role_display_name']); ?>
                                    (Level <?php echo $role['hierarchy_level']; ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Expires At (Optional)</label>
                        <input type="datetime-local" class="form-control" name="expires_at">
                        <small class="text-muted">Leave empty for permanent assignment</small>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus"></i> Assign Role
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-list-check"></i> Current Role Assignments
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($current_assignments)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                        <p>No role assignments found.</p>
                    </div>
                <?php else: ?>
                    <div style="max-height: 400px; overflow-y: auto;">
                        <?php foreach ($current_assignments as $assignment): ?>
                            <div class="assignment-card">
                                <div class="d-flex justify-content-between align-items-start">
                                    <div class="flex-grow-1">
                                        <strong><?php echo htmlspecialchars($assignment['username']); ?></strong>
                                        <?php if ($assignment['email']): ?>
                                            <small class="text-muted">(<?php echo htmlspecialchars($assignment['email']); ?>)</small>
                                        <?php endif; ?>
                                        <br>
                                        <span class="badge bg-primary"><?php echo htmlspecialchars($assignment['role_display_name']); ?></span>
                                        <br>
                                        <small class="text-muted">
                                            Assigned: <?php echo date('M j, Y g:i A', strtotime($assignment['assigned_at'])); ?>
                                        </small>
                                        <?php if ($assignment['expires_at']): ?>
                                            <br>
                                            <small class="text-warning">
                                                <i class="bi bi-clock"></i> Expires: <?php echo date('M j, Y g:i A', strtotime($assignment['expires_at'])); ?>
                                            </small>
                                        <?php else: ?>
                                            <br>
                                            <small class="text-success">
                                                <i class="bi bi-infinity"></i> Permanent
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-end">
                                        <div class="btn-group-vertical btn-group-sm" role="group">
                                            <!-- Extend/Modify Assignment -->
                                            <button type="button" class="btn btn-outline-warning btn-sm"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#extendRoleModal<?php echo $assignment['id']; ?>">
                                                <i class="bi bi-clock-history"></i> Extend
                                            </button>

                                            <!-- Delete Assignment -->
                                            <form method="POST" style="display: inline;"
                                                  onsubmit="return confirm('Remove role assignment for <?php echo htmlspecialchars($assignment['username']); ?>?')">
                                                <input type="hidden" name="action" value="delete_role_assignment">
                                                <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                                                <button type="submit" class="btn btn-outline-danger btn-sm">
                                                    <i class="bi bi-trash"></i> Remove
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Extend Role Modal -->
                            <div class="modal fade" id="extendRoleModal<?php echo $assignment['id']; ?>" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title">
                                                <i class="bi bi-clock-history"></i> Extend Role Assignment
                                            </h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                        </div>
                                        <form method="POST">
                                            <div class="modal-body">
                                                <input type="hidden" name="action" value="extend_role_assignment">
                                                <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">

                                                <p><strong>User:</strong> <?php echo htmlspecialchars($assignment['username']); ?></p>
                                                <p><strong>Role:</strong> <?php echo htmlspecialchars($assignment['role_display_name']); ?></p>

                                                <div class="mb-3">
                                                    <label class="form-label">New Expiry Date (Optional)</label>
                                                    <input type="datetime-local" class="form-control" name="new_expiry"
                                                           value="<?php echo $assignment['expires_at'] ? date('Y-m-d\TH:i', strtotime($assignment['expires_at'])) : ''; ?>">
                                                    <small class="text-muted">Leave empty to make permanent</small>
                                                </div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="submit" class="btn btn-warning">
                                                    <i class="bi bi-check"></i> Update Assignment
                                                </button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Session Assignment Section -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-calendar-event"></i> Session Assignments for Session Moderators
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($sessions)): ?>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle"></i>
                        <strong>No Sessions Available</strong>
                        <p class="mb-2">No active sessions found. You need to create sessions first before assigning them to moderators.</p>
                        <a href="event_sessions.php" class="btn btn-info">
                            <i class="bi bi-plus-circle"></i> Create Sessions
                        </a>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-person-check"></i> Assign Session to Moderator</h6>
                            <form method="POST">
                                <input type="hidden" name="action" value="assign_session">
                                <div class="mb-3">
                                    <label class="form-label">Select Session Moderator</label>
                                    <select class="form-select" name="user_id" required>
                                        <option value="">Choose moderator...</option>
                                        <?php
                                        // Get users with Session Moderator role
                                        $stmt = $pdo->query("
                                            SELECT DISTINCT a.id, a.username, a.email
                                            FROM admins a
                                            JOIN user_role_assignments ura ON a.id = ura.user_id
                                            JOIN user_roles ur ON ura.role_id = ur.id
                                            WHERE ur.role_name = 'session_moderator' AND ura.is_active = 1
                                            ORDER BY a.username
                                        ");
                                        $moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                        ?>
                                        <?php foreach ($moderators as $moderator): ?>
                                            <option value="<?php echo $moderator['id']; ?>">
                                                <?php echo htmlspecialchars($moderator['username']); ?>
                                                <?php if ($moderator['email']): ?>
                                                    (<?php echo htmlspecialchars($moderator['email']); ?>)
                                                <?php endif; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if (empty($moderators)): ?>
                                        <small class="text-muted">
                                            <i class="bi bi-exclamation-triangle"></i>
                                            No users with Session Moderator role found. Assign the role first above.
                                        </small>
                                    <?php endif; ?>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Select Session</label>
                                    <select class="form-select" name="session_id" required>
                                        <option value="">Choose session...</option>
                                        <?php foreach ($sessions as $session): ?>
                                            <option value="<?php echo $session['id']; ?>">
                                                <?php echo htmlspecialchars($session['session_title']); ?>
                                                (<?php echo htmlspecialchars($session['event_title']); ?> -
                                                <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-success" <?php echo empty($moderators) ? 'disabled' : ''; ?>>
                                    <i class="bi bi-check-circle"></i> Assign Session
                                </button>
                            </form>
                        </div>

                        <div class="col-md-6">
                            <h6><i class="bi bi-list-check"></i> Current Session Assignments</h6>
                            <?php
                            // Get current session assignments
                            try {
                                $stmt = $pdo->query("
                                    SELECT sa.id, sa.user_id, sa.session_id, a.username, a.email,
                                           es.session_title, e.title as event_title,
                                           es.start_datetime, es.end_datetime, sa.assigned_at
                                    FROM session_assignments sa
                                    JOIN admins a ON sa.user_id = a.id
                                    JOIN event_sessions es ON sa.session_id = es.id
                                    JOIN events e ON es.event_id = e.id
                                    WHERE sa.is_active = 1
                                    ORDER BY es.start_datetime ASC
                                ");
                                $session_assignments = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            } catch (PDOException $e) {
                                $session_assignments = [];
                            }
                            ?>

                            <?php if (empty($session_assignments)): ?>
                                <div class="text-center text-muted py-3">
                                    <i class="bi bi-calendar-x fs-3 d-block mb-2"></i>
                                    <p>No session assignments found.</p>
                                </div>
                            <?php else: ?>
                                <div style="max-height: 400px; overflow-y: auto;">
                                    <?php foreach ($session_assignments as $assignment): ?>
                                        <div class="assignment-card">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <div class="flex-grow-1">
                                                    <strong><?php echo htmlspecialchars($assignment['username']); ?></strong>
                                                    <?php if ($assignment['email']): ?>
                                                        <small class="text-muted">(<?php echo htmlspecialchars($assignment['email']); ?>)</small>
                                                    <?php endif; ?>
                                                    <br>
                                                    <span class="badge bg-success"><?php echo htmlspecialchars($assignment['session_title']); ?></span>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-calendar-event"></i> <?php echo htmlspecialchars($assignment['event_title']); ?>
                                                    </small>
                                                    <br>
                                                    <small class="text-info">
                                                        <i class="bi bi-clock"></i> <?php echo date('M j, Y g:i A', strtotime($assignment['start_datetime'])); ?>
                                                        <?php if ($assignment['end_datetime']): ?>
                                                            - <?php echo date('g:i A', strtotime($assignment['end_datetime'])); ?>
                                                        <?php endif; ?>
                                                    </small>
                                                    <br>
                                                    <small class="text-muted">
                                                        <i class="bi bi-person-check"></i> Assigned: <?php echo date('M j, Y', strtotime($assignment['assigned_at'])); ?>
                                                    </small>
                                                </div>
                                                <div class="text-end">
                                                    <div class="btn-group-vertical btn-group-sm" role="group">
                                                        <!-- View Session Details -->
                                                        <a href="event_sessions.php?session_id=<?php echo $assignment['session_id']; ?>"
                                                           class="btn btn-outline-info btn-sm" target="_blank">
                                                            <i class="bi bi-eye"></i> View
                                                        </a>

                                                        <!-- Reassign Session -->
                                                        <button type="button" class="btn btn-outline-warning btn-sm"
                                                                data-bs-toggle="modal"
                                                                data-bs-target="#reassignSessionModal<?php echo $assignment['id']; ?>">
                                                            <i class="bi bi-arrow-repeat"></i> Reassign
                                                        </button>

                                                        <!-- Remove Assignment -->
                                                        <form method="POST" style="display: inline;"
                                                              onsubmit="return confirm('Remove session assignment for <?php echo htmlspecialchars($assignment['username']); ?>?')">
                                                            <input type="hidden" name="action" value="delete_session_assignment">
                                                            <input type="hidden" name="assignment_id" value="<?php echo $assignment['id']; ?>">
                                                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                                                <i class="bi bi-trash"></i> Remove
                                                            </button>
                                                        </form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Reassign Session Modal -->
                                        <div class="modal fade" id="reassignSessionModal<?php echo $assignment['id']; ?>" tabindex="-1">
                                            <div class="modal-dialog">
                                                <div class="modal-content">
                                                    <div class="modal-header">
                                                        <h5 class="modal-title">
                                                            <i class="bi bi-arrow-repeat"></i> Reassign Session
                                                        </h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                                    </div>
                                                    <form method="POST">
                                                        <div class="modal-body">
                                                            <input type="hidden" name="action" value="assign_session">
                                                            <input type="hidden" name="session_id" value="<?php echo $assignment['session_id']; ?>">

                                                            <div class="alert alert-info">
                                                                <strong>Current Assignment:</strong><br>
                                                                <?php echo htmlspecialchars($assignment['username']); ?> →
                                                                <?php echo htmlspecialchars($assignment['session_title']); ?>
                                                            </div>

                                                            <div class="mb-3">
                                                                <label class="form-label">Reassign to New Moderator</label>
                                                                <select class="form-select" name="user_id" required>
                                                                    <option value="">Choose new moderator...</option>
                                                                    <?php
                                                                    // Get users with Session Moderator role (excluding current)
                                                                    $stmt = $pdo->prepare("
                                                                        SELECT DISTINCT a.id, a.username, a.email
                                                                        FROM admins a
                                                                        JOIN user_role_assignments ura ON a.id = ura.user_id
                                                                        JOIN user_roles ur ON ura.role_id = ur.id
                                                                        WHERE ur.role_name = 'session_moderator'
                                                                        AND ura.is_active = 1
                                                                        AND a.id != ?
                                                                        ORDER BY a.username
                                                                    ");
                                                                    $stmt->execute([$assignment['user_id']]);
                                                                    $other_moderators = $stmt->fetchAll(PDO::FETCH_ASSOC);
                                                                    ?>
                                                                    <?php foreach ($other_moderators as $moderator): ?>
                                                                        <option value="<?php echo $moderator['id']; ?>">
                                                                            <?php echo htmlspecialchars($moderator['username']); ?>
                                                                            <?php if ($moderator['email']): ?>
                                                                                (<?php echo htmlspecialchars($moderator['email']); ?>)
                                                                            <?php endif; ?>
                                                                        </option>
                                                                    <?php endforeach; ?>
                                                                </select>
                                                                <?php if (empty($other_moderators)): ?>
                                                                    <small class="text-muted">
                                                                        <i class="bi bi-exclamation-triangle"></i>
                                                                        No other session moderators available.
                                                                    </small>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                            <button type="submit" class="btn btn-warning" <?php echo empty($other_moderators) ? 'disabled' : ''; ?>>
                                                                <i class="bi bi-arrow-repeat"></i> Reassign Session
                                                            </button>
                                                        </div>
                                                    </form>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
