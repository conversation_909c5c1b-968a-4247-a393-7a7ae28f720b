<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file and RBAC system
require_once '../config.php';
require_once 'includes/rbac_system.php';

// Initialize RBAC system
$rbac = new RBACSystem($pdo);

// Check if RBAC system is initialized
if (!$rbac->isInitialized()) {
    header("Location: initialize_rbac_database.php");
    exit();
}

// Require Session Moderator role (or higher)
if (!$rbac->hasRole('session_moderator') && !$rbac->hasRole('event_coordinator') && !$rbac->hasRole('super_admin')) {
    header("Location: access_denied.php");
    exit();
}

// Log dashboard access
$rbac->logDashboardAccess('session_moderator_dashboard.php');

$message = '';
$error = '';

// Handle quick attendance marking
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'quick_mark_attended') {
            $session_id = $_POST['session_id'] ?? '';
            $attendee_ids = $_POST['attendee_ids'] ?? [];
            
            if (!$rbac->canManageSession($session_id)) {
                throw new Exception('You do not have permission to manage this session');
            }
            
            if (!empty($attendee_ids)) {
                $pdo->beginTransaction();
                
                $marked_count = 0;
                foreach ($attendee_ids as $attendee_id) {
                    // Parse attendee ID (format: member_123 or guest_name_email)
                    if (strpos($attendee_id, 'member_') === 0) {
                        $member_id = substr($attendee_id, 7);
                        $stmt = $pdo->prepare("
                            UPDATE session_attendance 
                            SET attendance_status = 'attended', attendance_date = NOW() 
                            WHERE session_id = ? AND member_id = ?
                        ");
                        $stmt->execute([$session_id, $member_id]);
                        $marked_count += $stmt->rowCount();
                    } else {
                        // Handle guest format: guest_name_email
                        $parts = explode('_', $attendee_id, 3);
                        if (count($parts) >= 3) {
                            $guest_name = $parts[1];
                            $guest_email = $parts[2];
                            $stmt = $pdo->prepare("
                                UPDATE session_attendance 
                                SET attendance_status = 'attended', attendance_date = NOW() 
                                WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                            ");
                            $stmt->execute([$session_id, $guest_name, $guest_email]);
                            $marked_count += $stmt->rowCount();
                        }
                    }
                }
                
                $pdo->commit();
                $message = "Successfully marked {$marked_count} attendees as attended.";
            }
            
        } elseif ($_POST['action'] === 'bulk_mark_all') {
            $session_id = $_POST['session_id'] ?? '';
            $status = $_POST['status'] ?? 'attended';
            
            if (!$rbac->canManageSession($session_id)) {
                throw new Exception('You do not have permission to manage this session');
            }
            
            $stmt = $pdo->prepare("
                UPDATE session_attendance 
                SET attendance_status = ?, attendance_date = NOW() 
                WHERE session_id = ?
            ");
            $stmt->execute([$status, $session_id]);
            $marked_count = $stmt->rowCount();
            
            $message = "Successfully marked {$marked_count} attendees as {$status}.";
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = $e->getMessage();
    }
}

// Get assigned sessions for current user
$assigned_sessions = $rbac->getAssignedSessions();

// Get detailed session information with attendance stats
$session_details = [];
foreach ($assigned_sessions as $session) {
    $stmt = $pdo->prepare("
        SELECT 
            sa.*,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN m.full_name
                ELSE sa.guest_name
            END as attendee_name,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN m.email
                ELSE sa.guest_email
            END as attendee_email,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN 'member'
                ELSE 'guest'
            END as attendee_type,
            CASE 
                WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
            END as attendee_id
        FROM session_attendance sa
        LEFT JOIN members m ON sa.member_id = m.id
        WHERE sa.session_id = ?
        ORDER BY attendee_name ASC
    ");
    $stmt->execute([$session['id']]);
    $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate stats
    $total_registered = count($attendees);
    $total_attended = count(array_filter($attendees, function($a) { return $a['attendance_status'] === 'attended'; }));
    $total_no_show = count(array_filter($attendees, function($a) { return $a['attendance_status'] === 'no_show'; }));
    $total_pending = $total_registered - $total_attended - $total_no_show;
    
    $session_details[] = [
        'session' => $session,
        'attendees' => $attendees,
        'stats' => [
            'total_registered' => $total_registered,
            'total_attended' => $total_attended,
            'total_no_show' => $total_no_show,
            'total_pending' => $total_pending,
            'attendance_rate' => $total_registered > 0 ? round(($total_attended / $total_registered) * 100, 1) : 0
        ]
    ];
}

// Page title and header info
$page_title = 'Session Moderator Dashboard';
$page_header = 'Session Moderator Dashboard';
$page_description = 'Manage attendance for your assigned sessions';

// Include header
include 'includes/header.php';
?>

<style>
.moderator-header {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border-radius: 10px;
}
.session-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    border-radius: 10px;
}
.session-card.active {
    border-color: #28a745;
    background-color: #f8fff9;
}
.session-card.upcoming {
    border-color: #ffc107;
    background-color: #fffbf0;
}
.session-card.completed {
    border-color: #6c757d;
    background-color: #f8f9fa;
}
.attendee-row {
    transition: background-color 0.2s ease;
    cursor: pointer;
    border-radius: 5px;
    margin: 2px 0;
    padding: 8px;
}
.attendee-row:hover {
    background-color: #f8f9fa;
}
.attendee-row.selected {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
}
.quick-mark-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}
.mobile-optimized {
    font-size: 1.1rem;
}
@media (max-width: 768px) {
    .mobile-optimized {
        font-size: 1.2rem;
    }
    .attendee-row {
        padding: 12px;
        margin: 4px 0;
    }
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card moderator-header">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-person-video2"></i> Session Moderator Dashboard
                        </h2>
                        <p class="text-white-50 mb-0">Manage attendance for your assigned sessions</p>
                    </div>
                    <div class="text-end">
                        <div class="btn-group">
                            <button class="btn btn-outline-light" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <?php if ($rbac->hasRole('super_admin')): ?>
                                <a href="super_admin_dashboard.php" class="btn btn-outline-light">
                                    <i class="bi bi-speedometer2"></i> Super Admin
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- No Sessions Assigned -->
<?php if (empty($session_details)): ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-calendar-x text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">No Sessions Assigned</h4>
                    <p class="lead text-muted">You don't have any sessions assigned to moderate yet.</p>
                    <div class="mt-4">
                        <p class="text-muted">Contact your administrator to get assigned to sessions.</p>
                        <?php if ($rbac->hasRole('super_admin')): ?>
                            <a href="setup_rbac_system.php" class="btn btn-primary">
                                <i class="bi bi-person-plus"></i> Assign Sessions
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>

<!-- Assigned Sessions -->
<?php foreach ($session_details as $index => $detail):
    $session = $detail['session'];
    $attendees = $detail['attendees'];
    $stats = $detail['stats'];

    // Determine session status
    $now = new DateTime();
    $start_time = new DateTime($session['start_datetime']);
    $end_time = new DateTime($session['end_datetime']);

    if ($now < $start_time) {
        $status = 'upcoming';
        $status_text = 'Upcoming';
        $status_icon = 'clock';
    } elseif ($now >= $start_time && $now <= $end_time) {
        $status = 'active';
        $status_text = 'Active Now';
        $status_icon = 'play-circle';
    } else {
        $status = 'completed';
        $status_text = 'Completed';
        $status_icon = 'check-circle';
    }
?>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card session-card <?php echo $status; ?>">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="bi bi-<?php echo $status_icon; ?>"></i>
                            <?php echo htmlspecialchars($session['session_title']); ?>
                        </h5>
                        <small class="text-muted">
                            <?php echo htmlspecialchars($session['event_title']); ?>
                            • <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                            <?php if ($session['location']): ?>
                                • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                            <?php endif; ?>
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-<?php
                            echo $status === 'active' ? 'success' :
                                ($status === 'upcoming' ? 'warning' : 'secondary');
                        ?> fs-6">
                            <?php echo $status_text; ?>
                        </span>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- Session Statistics -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="card stats-card">
                            <div class="card-body py-3">
                                <div class="row text-center">
                                    <div class="col-3">
                                        <h4 class="text-white mb-0"><?php echo $stats['total_registered']; ?></h4>
                                        <small class="text-white-50">Registered</small>
                                    </div>
                                    <div class="col-3">
                                        <h4 class="text-white mb-0"><?php echo $stats['total_attended']; ?></h4>
                                        <small class="text-white-50">Attended</small>
                                    </div>
                                    <div class="col-3">
                                        <h4 class="text-white mb-0"><?php echo $stats['total_pending']; ?></h4>
                                        <small class="text-white-50">Pending</small>
                                    </div>
                                    <div class="col-3">
                                        <h4 class="text-white mb-0"><?php echo $stats['attendance_rate']; ?>%</h4>
                                        <small class="text-white-50">Rate</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="btn-group w-100" role="group">
                            <button class="btn btn-success" onclick="selectAllAttendees(<?php echo $session['id']; ?>)">
                                <i class="bi bi-check-all"></i> Select All
                            </button>
                            <button class="btn btn-outline-secondary" onclick="selectNoneAttendees(<?php echo $session['id']; ?>)">
                                <i class="bi bi-x"></i> Select None
                            </button>
                            <button class="btn btn-primary" onclick="markSelectedAttended(<?php echo $session['id']; ?>)">
                                <i class="bi bi-check-circle"></i> Mark Selected Attended
                            </button>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="bulk_mark_all">
                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                <input type="hidden" name="status" value="attended">
                                <button type="submit" class="btn btn-warning"
                                        onclick="return confirm('Mark ALL registered attendees as attended?')">
                                    <i class="bi bi-lightning"></i> Mark All Attended
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Attendee List -->
                <div class="row">
                    <div class="col-md-12">
                        <h6>
                            <i class="bi bi-people"></i> Attendees
                            <span class="badge bg-primary" id="selected-count-<?php echo $session['id']; ?>">0 selected</span>
                        </h6>

                        <?php if (empty($attendees)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                                <p>No attendees registered for this session.</p>
                            </div>
                        <?php else: ?>
                            <div class="attendee-list mobile-optimized" style="max-height: 400px; overflow-y: auto;">
                                <?php foreach ($attendees as $attendee): ?>
                                    <div class="attendee-row"
                                         onclick="toggleAttendeeSelection('<?php echo $attendee['attendee_id']; ?>', <?php echo $session['id']; ?>)">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <input type="checkbox"
                                                       class="form-check-input me-3 attendee-checkbox"
                                                       id="attendee_<?php echo $attendee['attendee_id']; ?>_<?php echo $session['id']; ?>"
                                                       value="<?php echo $attendee['attendee_id']; ?>"
                                                       data-session="<?php echo $session['id']; ?>"
                                                       onclick="event.stopPropagation();"
                                                       onchange="updateAttendeeSelection(<?php echo $session['id']; ?>)">
                                                <div>
                                                    <strong><?php echo htmlspecialchars($attendee['attendee_name']); ?></strong>
                                                    <?php if ($attendee['attendee_email']): ?>
                                                        <br>
                                                        <small class="text-muted">
                                                            <i class="bi bi-envelope"></i> <?php echo htmlspecialchars($attendee['attendee_email']); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-<?php
                                                    echo $attendee['attendance_status'] === 'attended' ? 'success' :
                                                        ($attendee['attendance_status'] === 'no_show' ? 'danger' : 'secondary');
                                                ?>">
                                                    <?php echo ucfirst($attendee['attendance_status']); ?>
                                                </span>
                                                <br>
                                                <small class="text-muted">
                                                    <span class="badge bg-<?php echo $attendee['attendee_type'] === 'member' ? 'primary' : 'info'; ?>">
                                                        <?php echo ucfirst($attendee['attendee_type']); ?>
                                                    </span>
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php endforeach; ?>
<?php endif; ?>

<!-- Quick Mark Button (Mobile) -->
<button class="btn btn-success btn-lg quick-mark-btn d-md-none" onclick="showQuickMarkModal()">
    <i class="bi bi-check-circle"></i>
</button>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Session moderator dashboard functionality
function refreshDashboard() {
    location.reload();
}

function selectAllAttendees(sessionId) {
    const checkboxes = document.querySelectorAll(`input[data-session="${sessionId}"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        const row = checkbox.closest('.attendee-row');
        row.classList.add('selected');
    });
    updateAttendeeSelection(sessionId);
}

function selectNoneAttendees(sessionId) {
    const checkboxes = document.querySelectorAll(`input[data-session="${sessionId}"]`);
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        const row = checkbox.closest('.attendee-row');
        row.classList.remove('selected');
    });
    updateAttendeeSelection(sessionId);
}

function toggleAttendeeSelection(attendeeId, sessionId) {
    const checkbox = document.getElementById(`attendee_${attendeeId}_${sessionId}`);
    checkbox.checked = !checkbox.checked;

    const row = checkbox.closest('.attendee-row');
    if (checkbox.checked) {
        row.classList.add('selected');
    } else {
        row.classList.remove('selected');
    }

    updateAttendeeSelection(sessionId);
}

function updateAttendeeSelection(sessionId) {
    const checkboxes = document.querySelectorAll(`input[data-session="${sessionId}"]:checked`);
    const count = checkboxes.length;
    const countElement = document.getElementById(`selected-count-${sessionId}`);

    if (countElement) {
        countElement.textContent = `${count} selected`;
        countElement.className = count > 0 ? 'badge bg-success' : 'badge bg-primary';
    }

    // Update row visual states
    document.querySelectorAll(`input[data-session="${sessionId}"]`).forEach(checkbox => {
        const row = checkbox.closest('.attendee-row');
        if (checkbox.checked) {
            row.classList.add('selected');
        } else {
            row.classList.remove('selected');
        }
    });
}

function markSelectedAttended(sessionId) {
    const checkboxes = document.querySelectorAll(`input[data-session="${sessionId}"]:checked`);
    const attendeeIds = Array.from(checkboxes).map(cb => cb.value);

    if (attendeeIds.length === 0) {
        alert('Please select at least one attendee to mark as attended.');
        return;
    }

    if (!confirm(`Mark ${attendeeIds.length} selected attendees as attended?`)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.style.display = 'none';

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'quick_mark_attended';
    form.appendChild(actionInput);

    const sessionInput = document.createElement('input');
    sessionInput.type = 'hidden';
    sessionInput.name = 'session_id';
    sessionInput.value = sessionId;
    form.appendChild(sessionInput);

    attendeeIds.forEach(id => {
        const attendeeInput = document.createElement('input');
        attendeeInput.type = 'hidden';
        attendeeInput.name = 'attendee_ids[]';
        attendeeInput.value = id;
        form.appendChild(attendeeInput);
    });

    document.body.appendChild(form);
    form.submit();
}

function showQuickMarkModal() {
    // For mobile - show a simplified interface
    alert('Quick Mark functionality for mobile interface would be implemented here');
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all selection counters
    <?php foreach ($session_details as $detail): ?>
        updateAttendeeSelection(<?php echo $detail['session']['id']; ?>);
    <?php endforeach; ?>

    // Add touch-friendly interactions for mobile
    if (window.innerWidth <= 768) {
        document.querySelectorAll('.attendee-row').forEach(row => {
            row.style.minHeight = '60px';
            row.style.padding = '15px';
        });
    }

    // Auto-refresh every 2 minutes for active sessions
    setInterval(function() {
        const activeCards = document.querySelectorAll('.session-card.active');
        if (activeCards.length > 0) {
            // Only refresh if there are active sessions
            location.reload();
        }
    }, 120000); // 2 minutes
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + A to select all in first session
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault();
        const firstSession = document.querySelector('[data-session]');
        if (firstSession) {
            const sessionId = firstSession.getAttribute('data-session');
            selectAllAttendees(sessionId);
        }
    }

    // Ctrl/Cmd + Enter to mark selected as attended in first session
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        const firstSession = document.querySelector('[data-session]');
        if (firstSession) {
            const sessionId = firstSession.getAttribute('data-session');
            markSelectedAttended(sessionId);
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
