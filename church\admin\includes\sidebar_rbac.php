<?php
/**
 * Role-Based Access Control Sidebar
 * Dynamic sidebar that changes based on user roles and permissions
 */

// Include RBAC system
require_once 'rbac_access_control.php';

// Get current page for active state
$current_page = basename($_SERVER['PHP_SELF']);

// Initialize RBAC if not already done
if (!isset($rbac) || !($rbac instanceof RBACAccessControl)) {
    global $pdo;

    // Ensure database connection exists
    if (!isset($pdo)) {
        require_once '../config.php';
    }

    try {
        $rbac = new RBACAccessControl($pdo, $_SESSION['admin_id'] ?? null);
    } catch (Exception $e) {
        // Fallback to original sidebar if RBAC fails
        include 'sidebar.php';
        return;
    }
}

// Get sidebar items based on user role with error handling
try {
    $sidebar_items = $rbac->getSidebarItems();
    $user_roles = $rbac->getUserRoles();
    $primary_role = $rbac->getPrimaryRole();
} catch (Exception $e) {
    // Fallback to basic sidebar items if RBAC fails
    $sidebar_items = [
        'dashboard' => [
            'title' => 'Dashboard',
            'url' => 'dashboard.php',
            'icon' => 'bi-speedometer2'
        ],
        'members' => [
            'title' => 'Members',
            'url' => 'members.php',
            'icon' => 'bi-people'
        ]
    ];
    $user_roles = [];
    $primary_role = 'admin';
}

// Helper function to check if current page is active
function is_active_rbac($page) {
    global $current_page;
    return $current_page === $page ? 'active' : '';
}

// Helper function to generate admin URL
function admin_url_for_rbac($page) {
    return $page;
}
?>

<!-- Role-Based Sidebar -->
<div class="sidebar" id="sidebar">
    <!-- Sidebar Header with User Role Info -->
    <div class="sidebar-header">
        <div class="d-flex align-items-center">
            <img src="assets/images/logo.png" alt="Logo" class="sidebar-logo me-2" style="width: 40px; height: 40px;">
            <div>
                <h6 class="mb-0 text-white">Church Admin</h6>
                <?php if (!empty($user_roles)): ?>
                    <small class="text-white-50">
                        <?php echo htmlspecialchars($user_roles[0]['role_display_name']); ?>
                    </small>
                <?php endif; ?>
            </div>
        </div>
        <button class="btn btn-sm btn-outline-light d-md-none" type="button" onclick="toggleSidebar()">
            <i class="bi bi-x"></i>
        </button>
    </div>

    <!-- Navigation Menu -->
    <nav class="sidebar-nav">
        <ul class="nav flex-column">
            <?php foreach ($sidebar_items as $key => $item): ?>
                <?php if (isset($item['items'])): ?>
                    <!-- Menu Section with Submenu -->
                    <li class="nav-item mt-2 mb-1 category-header">
                        <div class="sidebar-heading px-3 text-muted text-uppercase small">
                            <i class="<?php echo $item['icon']; ?>"></i> 
                            <span class="menu-text"><?php echo htmlspecialchars($item['title']); ?></span>
                        </div>
                    </li>
                    <?php foreach ($item['items'] as $subitem): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo is_active_rbac($subitem['url']); ?>" 
                               href="<?php echo admin_url_for_rbac($subitem['url']); ?>" 
                               title="<?php echo htmlspecialchars($subitem['title']); ?>">
                                <i class="<?php echo $subitem['icon']; ?>"></i> 
                                <span class="menu-text"><?php echo htmlspecialchars($subitem['title']); ?></span>
                            </a>
                        </li>
                    <?php endforeach; ?>
                <?php else: ?>
                    <!-- Single Menu Item -->
                    <li class="nav-item">
                        <a class="nav-link <?php echo is_active_rbac($item['url']); ?>" 
                           href="<?php echo admin_url_for_rbac($item['url']); ?>" 
                           title="<?php echo htmlspecialchars($item['title']); ?>">
                            <i class="<?php echo $item['icon']; ?>"></i> 
                            <span class="menu-text"><?php echo htmlspecialchars($item['title']); ?></span>
                        </a>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>

            <!-- Logout (Always Available) -->
            <li class="nav-item mt-4">
                <a class="nav-link text-danger" href="logout.php" title="Logout">
                    <i class="bi bi-box-arrow-right"></i> 
                    <span class="menu-text">Logout</span>
                </a>
            </li>
        </ul>
    </nav>

    <!-- Role Information Footer -->
    <div class="sidebar-footer">
        <div class="px-3 py-2">
            <small class="text-white-50">
                <i class="bi bi-shield-check"></i> 
                Access Level: <?php echo ucwords(str_replace('_', ' ', $primary_role)); ?>
            </small>
            <?php if (count($user_roles) > 1): ?>
                <br>
                <small class="text-white-50">
                    <i class="bi bi-plus-circle"></i> 
                    +<?php echo count($user_roles) - 1; ?> more role(s)
                </small>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Sidebar Styles -->
<style>
.sidebar {
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    width: 250px;
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
    color: white;
    z-index: 1000;
    overflow-y: auto;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 1rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
}

.sidebar-logo {
    border-radius: 50%;
    object-fit: cover;
}

.sidebar-nav {
    padding: 1rem 0;
    flex: 1;
}

.sidebar-footer {
    border-top: 1px solid rgba(255,255,255,0.1);
    background: rgba(0,0,0,0.1);
    margin-top: auto;
}

.nav-link {
    color: rgba(255,255,255,0.8);
    padding: 0.75rem 1rem;
    border-radius: 0;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.nav-link:hover {
    color: white;
    background-color: rgba(255,255,255,0.1);
    border-left-color: #3498db;
}

.nav-link.active {
    color: white;
    background-color: rgba(52, 152, 219, 0.2);
    border-left-color: #3498db;
}

.category-header .sidebar-heading {
    font-weight: 600;
    letter-spacing: 0.5px;
    margin-bottom: 0.5rem;
}

.menu-text {
    margin-left: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0 !important;
    }
}

@media (min-width: 769px) {
    .main-content {
        margin-left: 250px;
        transition: margin-left 0.3s ease;
    }
}

/* Role-specific styling */
.sidebar[data-role="super_admin"] {
    background: linear-gradient(180deg, #8e44ad 0%, #9b59b6 100%);
}

.sidebar[data-role="event_coordinator"] {
    background: linear-gradient(180deg, #e67e22 0%, #f39c12 100%);
}

.sidebar[data-role="session_moderator"] {
    background: linear-gradient(180deg, #27ae60 0%, #2ecc71 100%);
}

.sidebar[data-role="staff"] {
    background: linear-gradient(180deg, #3498db 0%, #5dade2 100%);
}

.sidebar[data-role="limited_admin"] {
    background: linear-gradient(180deg, #34495e 0%, #5d6d7e 100%);
}
</style>

<script>
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar.classList.toggle('show');
}

// Close sidebar when clicking outside on mobile
document.addEventListener('click', function(event) {
    const sidebar = document.getElementById('sidebar');
    const toggleBtn = document.querySelector('[onclick="toggleSidebar()"]');
    
    if (window.innerWidth <= 768 && 
        !sidebar.contains(event.target) && 
        !toggleBtn.contains(event.target)) {
        sidebar.classList.remove('show');
    }
});

// Set role-specific styling
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    sidebar.setAttribute('data-role', '<?php echo $primary_role; ?>');
});
</script>
