<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file and RBAC system
require_once '../config.php';
require_once 'includes/rbac_system.php';

// Initialize RBAC system
$rbac = new RBACSystem($pdo);

// Check if RBAC system is initialized
if (!$rbac->isInitialized()) {
    header("Location: initialize_rbac_database.php");
    exit();
}

// Require Staff role (or higher)
if (!$rbac->hasRole('staff') && !$rbac->hasRole('session_moderator') && !$rbac->hasRole('event_coordinator') && !$rbac->hasRole('super_admin')) {
    header("Location: access_denied.php");
    exit();
}

// Log dashboard access
$rbac->logDashboardAccess('staff_dashboard.php');

$message = '';
$error = '';

// Handle quick attendance marking
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        if ($_POST['action'] === 'mark_individual') {
            $session_id = $_POST['session_id'] ?? '';
            $attendee_id = $_POST['attendee_id'] ?? '';
            $status = $_POST['status'] ?? 'attended';
            
            if (!empty($session_id) && !empty($attendee_id)) {
                // Parse attendee ID (format: member_123 or guest_name_email)
                if (strpos($attendee_id, 'member_') === 0) {
                    $member_id = substr($attendee_id, 7);
                    $stmt = $pdo->prepare("
                        UPDATE session_attendance 
                        SET attendance_status = ?, attendance_date = NOW() 
                        WHERE session_id = ? AND member_id = ?
                    ");
                    $stmt->execute([$status, $session_id, $member_id]);
                } else {
                    // Handle guest format: guest_name_email
                    $parts = explode('_', $attendee_id, 3);
                    if (count($parts) >= 3) {
                        $guest_name = $parts[1];
                        $guest_email = $parts[2];
                        $stmt = $pdo->prepare("
                            UPDATE session_attendance 
                            SET attendance_status = ?, attendance_date = NOW() 
                            WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                        ");
                        $stmt->execute([$status, $session_id, $guest_name, $guest_email]);
                    }
                }
                
                $message = "Attendance updated successfully.";
            }
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get current and upcoming sessions (staff can see sessions they might need to help with)
$stmt = $pdo->query("
    SELECT 
        es.*,
        e.title as event_title,
        COUNT(sa.id) as total_registered,
        COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
        CASE 
            WHEN es.start_datetime > NOW() THEN 'upcoming'
            WHEN es.start_datetime <= NOW() AND es.end_datetime >= NOW() THEN 'active'
            ELSE 'completed'
        END as session_status
    FROM event_sessions es
    JOIN events e ON es.event_id = e.id
    LEFT JOIN session_attendance sa ON es.id = sa.session_id
    WHERE es.status = 'active' 
    AND e.is_active = 1
    AND es.start_datetime >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
    AND es.start_datetime <= DATE_ADD(NOW(), INTERVAL 4 HOUR)
    GROUP BY es.id
    ORDER BY es.start_datetime ASC
    LIMIT 10
");
$current_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get session details for active sessions
$active_session_details = [];
foreach ($current_sessions as $session) {
    if ($session['session_status'] === 'active') {
        $stmt = $pdo->prepare("
            SELECT 
                sa.*,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN m.full_name
                    ELSE sa.guest_name
                END as attendee_name,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN 'member'
                    ELSE 'guest'
                END as attendee_type,
                CASE 
                    WHEN sa.member_id IS NOT NULL THEN CONCAT('member_', sa.member_id)
                    ELSE CONCAT('guest_', sa.guest_name, '_', sa.guest_email)
                END as attendee_id
            FROM session_attendance sa
            LEFT JOIN members m ON sa.member_id = m.id
            WHERE sa.session_id = ?
            ORDER BY attendee_name ASC
        ");
        $stmt->execute([$session['id']]);
        $attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        $active_session_details[] = [
            'session' => $session,
            'attendees' => $attendees
        ];
    }
}

// Page title and header info
$page_title = 'Staff Dashboard';
$page_header = 'Staff Dashboard';
$page_description = 'Quick check-in and basic attendance marking';

// Include header
include 'includes/header.php';
?>

<style>
.staff-header {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border-radius: 10px;
}
.session-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin-bottom: 20px;
}
.session-card.active {
    border-color: #28a745;
    background-color: #f8fff9;
}
.session-card.upcoming {
    border-color: #ffc107;
    background-color: #fffbf0;
}
.attendee-item {
    padding: 12px;
    margin: 5px 0;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    transition: all 0.2s ease;
}
.attendee-item:hover {
    background-color: #f8f9fa;
}
.attendee-item.attended {
    background-color: #d4edda;
    border-color: #28a745;
}
.attendee-item.no_show {
    background-color: #f8d7da;
    border-color: #dc3545;
}
.quick-mark-buttons {
    display: flex;
    gap: 5px;
}
.mobile-friendly {
    font-size: 1.1rem;
    padding: 15px;
}
@media (max-width: 768px) {
    .mobile-friendly {
        font-size: 1.3rem;
        padding: 20px;
    }
    .quick-mark-buttons .btn {
        padding: 10px 15px;
        font-size: 1.1rem;
    }
}
.stats-simple {
    background: linear-gradient(135deg, #17a2b8 0%, #007bff 100%);
    color: white;
    border-radius: 10px;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card staff-header">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-person-check"></i> Staff Dashboard
                        </h2>
                        <p class="text-white-50 mb-0">Quick check-in and basic attendance marking</p>
                    </div>
                    <div class="text-end">
                        <div class="btn-group">
                            <button class="btn btn-outline-light" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Current Sessions Overview -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-clock"></i> Current & Upcoming Sessions
                    <small class="text-muted ms-2">(Next 4 hours)</small>
                </h5>
            </div>
            <div class="card-body">
                <?php if (empty($current_sessions)): ?>
                    <div class="text-center text-muted py-4">
                        <i class="bi bi-calendar-x fs-3 d-block mb-2"></i>
                        <p>No sessions scheduled for the next few hours.</p>
                        <small class="text-muted">Check back later or contact your coordinator.</small>
                    </div>
                <?php else: ?>
                    <div class="row">
                        <?php foreach ($current_sessions as $session): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="session-card <?php echo $session['session_status']; ?>">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                            <span class="badge bg-<?php 
                                                echo $session['session_status'] === 'active' ? 'success' : 
                                                    ($session['session_status'] === 'upcoming' ? 'warning' : 'secondary'); 
                                            ?>">
                                                <?php echo ucfirst($session['session_status']); ?>
                                            </span>
                                        </div>
                                        
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <strong><?php echo htmlspecialchars($session['event_title']); ?></strong>
                                                <br>
                                                <i class="bi bi-clock"></i> <?php echo date('g:i A', strtotime($session['start_datetime'])); ?>
                                                <?php if ($session['location']): ?>
                                                    <br><i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                        
                                        <div class="card stats-simple">
                                            <div class="card-body py-2">
                                                <div class="row text-center">
                                                    <div class="col-6">
                                                        <h6 class="text-white mb-0"><?php echo $session['total_attended']; ?></h6>
                                                        <small class="text-white-50">Attended</small>
                                                    </div>
                                                    <div class="col-6">
                                                        <h6 class="text-white mb-0"><?php echo $session['total_registered']; ?></h6>
                                                        <small class="text-white-50">Registered</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <?php if ($session['session_status'] === 'active'): ?>
                                            <div class="mt-2">
                                                <a href="#session-<?php echo $session['id']; ?>" class="btn btn-success btn-sm w-100">
                                                    <i class="bi bi-check-circle"></i> Mark Attendance
                                                </a>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Active Session Attendance -->
<?php foreach ($active_session_details as $detail):
    $session = $detail['session'];
    $attendees = $detail['attendees'];
?>

<div class="row mb-4" id="session-<?php echo $session['id']; ?>">
    <div class="col-md-12">
        <div class="card session-card active">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-broadcast text-success"></i>
                    Active Session: <?php echo htmlspecialchars($session['session_title']); ?>
                </h5>
                <small class="text-muted">
                    <?php echo htmlspecialchars($session['event_title']); ?>
                    • Started: <?php echo date('g:i A', strtotime($session['start_datetime'])); ?>
                </small>
            </div>

            <div class="card-body mobile-friendly">
                <?php if (empty($attendees)): ?>
                    <div class="text-center text-muted py-3">
                        <i class="bi bi-person-x fs-3 d-block mb-2"></i>
                        <p>No attendees registered for this session.</p>
                    </div>
                <?php else: ?>
                    <div class="mb-3">
                        <h6>
                            <i class="bi bi-people"></i> Quick Check-In
                            <small class="text-muted">(<?php echo count($attendees); ?> registered)</small>
                        </h6>
                    </div>

                    <div class="attendee-list">
                        <?php foreach ($attendees as $attendee): ?>
                            <div class="attendee-item <?php echo $attendee['attendance_status']; ?>">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong><?php echo htmlspecialchars($attendee['attendee_name']); ?></strong>
                                        <span class="badge bg-<?php echo $attendee['attendee_type'] === 'member' ? 'primary' : 'info'; ?> ms-2">
                                            <?php echo ucfirst($attendee['attendee_type']); ?>
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            Current Status:
                                            <span class="badge bg-<?php
                                                echo $attendee['attendance_status'] === 'attended' ? 'success' :
                                                    ($attendee['attendance_status'] === 'no_show' ? 'danger' : 'secondary');
                                            ?>">
                                                <?php echo ucfirst($attendee['attendance_status']); ?>
                                            </span>
                                        </small>
                                    </div>

                                    <div class="quick-mark-buttons">
                                        <?php if ($attendee['attendance_status'] !== 'attended'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_individual">
                                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                                <input type="hidden" name="attendee_id" value="<?php echo $attendee['attendee_id']; ?>">
                                                <input type="hidden" name="status" value="attended">
                                                <button type="submit" class="btn btn-success">
                                                    <i class="bi bi-check-circle"></i> Present
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <?php if ($attendee['attendance_status'] !== 'no_show'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_individual">
                                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                                <input type="hidden" name="attendee_id" value="<?php echo $attendee['attendee_id']; ?>">
                                                <input type="hidden" name="status" value="no_show">
                                                <button type="submit" class="btn btn-outline-danger">
                                                    <i class="bi bi-x-circle"></i> Absent
                                                </button>
                                            </form>
                                        <?php endif; ?>

                                        <?php if ($attendee['attendance_status'] !== 'registered'): ?>
                                            <form method="POST" style="display: inline;">
                                                <input type="hidden" name="action" value="mark_individual">
                                                <input type="hidden" name="session_id" value="<?php echo $session['id']; ?>">
                                                <input type="hidden" name="attendee_id" value="<?php echo $attendee['attendee_id']; ?>">
                                                <input type="hidden" name="status" value="registered">
                                                <button type="submit" class="btn btn-outline-secondary">
                                                    <i class="bi bi-arrow-clockwise"></i> Reset
                                                </button>
                                            </form>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?php endforeach; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Staff dashboard functionality
function refreshDashboard() {
    location.reload();
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh every 2 minutes during active sessions
    const activeSessions = document.querySelectorAll('.session-card.active');
    if (activeSessions.length > 0) {
        setInterval(function() {
            location.reload();
        }, 120000); // 2 minutes
    }

    // Add smooth scrolling to session links
    document.querySelectorAll('a[href^="#session-"]').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({ behavior: 'smooth' });
            }
        });
    });
});
</script>

<?php include 'includes/footer.php'; ?>
