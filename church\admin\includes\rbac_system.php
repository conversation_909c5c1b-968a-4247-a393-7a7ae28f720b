<?php
/**
 * Role-Based Access Control (RBAC) System
 * 
 * This class manages hierarchical dashboard access control with role-based permissions
 * for the multi-tier event management system.
 */

class RBACSystem {
    private $pdo;
    private $current_user_id;
    private $current_user_roles;
    private $current_user_permissions;
    
    public function __construct($pdo, $user_id = null) {
        $this->pdo = $pdo;
        $this->current_user_id = $user_id ?? $_SESSION['admin_id'] ?? null;
        $this->loadUserRolesAndPermissions();
    }
    
    /**
     * Load current user's roles and permissions
     */
    private function loadUserRolesAndPermissions() {
        if (!$this->current_user_id) {
            $this->current_user_roles = [];
            $this->current_user_permissions = [];
            return;
        }
        
        // Load user roles
        $stmt = $this->pdo->prepare("
            SELECT ur.*, ura.assigned_at, ura.expires_at
            FROM user_roles ur
            JOIN user_role_assignments ura ON ur.id = ura.role_id
            WHERE ura.user_id = ? AND ura.is_active = 1
            AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
            ORDER BY ur.hierarchy_level ASC
        ");
        $stmt->execute([$this->current_user_id]);
        $this->current_user_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Load user permissions
        $role_ids = array_column($this->current_user_roles, 'id');
        if (!empty($role_ids)) {
            $placeholders = str_repeat('?,', count($role_ids) - 1) . '?';
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT p.*
                FROM permissions p
                JOIN role_permissions rp ON p.id = rp.permission_id
                WHERE rp.role_id IN ($placeholders)
            ");
            $stmt->execute($role_ids);
            $this->current_user_permissions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            $this->current_user_permissions = [];
        }
    }
    
    /**
     * Check if user has a specific role
     */
    public function hasRole($role_name) {
        foreach ($this->current_user_roles as $role) {
            if ($role['role_name'] === $role_name) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Check if user has a specific permission
     */
    public function hasPermission($permission_name) {
        foreach ($this->current_user_permissions as $permission) {
            if ($permission['permission_name'] === $permission_name) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get user's highest role (lowest hierarchy level)
     */
    public function getHighestRole() {
        if (empty($this->current_user_roles)) {
            return null;
        }
        return $this->current_user_roles[0]; // Already sorted by hierarchy_level ASC
    }
    
    /**
     * Get appropriate dashboard URL for user
     */
    public function getDashboardUrl() {
        $highest_role = $this->getHighestRole();
        if ($highest_role) {
            return $highest_role['dashboard_route'];
        }
        return 'staff_dashboard.php'; // Default fallback
    }
    
    /**
     * Check if user can access a specific dashboard
     */
    public function canAccessDashboard($dashboard_name) {
        foreach ($this->current_user_roles as $role) {
            if ($role['dashboard_route'] === $dashboard_name) {
                return true;
            }
        }
        
        // Super admins can access all dashboards
        return $this->hasRole('super_admin');
    }
    
    /**
     * Get events assigned to current user
     */
    public function getAssignedEvents() {
        if (!$this->current_user_id) {
            return [];
        }
        
        // Super admins see all events
        if ($this->hasRole('super_admin')) {
            $stmt = $this->pdo->query("SELECT * FROM events WHERE is_active = 1 ORDER BY event_date DESC");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        // Get assigned events for coordinators and organizers
        $stmt = $this->pdo->prepare("
            SELECT DISTINCT e.*, ea.role_type, ea.assigned_at
            FROM events e
            JOIN event_assignments ea ON e.id = ea.event_id
            WHERE ea.user_id = ? AND ea.is_active = 1 AND e.is_active = 1
            ORDER BY e.event_date DESC
        ");
        $stmt->execute([$this->current_user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get sessions assigned to current user
     */
    public function getAssignedSessions() {
        if (!$this->current_user_id) {
            return [];
        }
        
        // Super admins see all sessions
        if ($this->hasRole('super_admin')) {
            $stmt = $this->pdo->query("
                SELECT es.*, e.title as event_title
                FROM event_sessions es
                JOIN events e ON es.event_id = e.id
                WHERE es.status = 'active' AND e.is_active = 1
                ORDER BY es.start_datetime ASC
            ");
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        // Event coordinators see all sessions in their assigned events
        if ($this->hasRole('event_coordinator')) {
            $stmt = $this->pdo->prepare("
                SELECT DISTINCT es.*, e.title as event_title
                FROM event_sessions es
                JOIN events e ON es.event_id = e.id
                JOIN event_assignments ea ON e.id = ea.event_id
                WHERE ea.user_id = ? AND ea.is_active = 1 
                AND es.status = 'active' AND e.is_active = 1
                ORDER BY es.start_datetime ASC
            ");
            $stmt->execute([$this->current_user_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        // Session moderators see only their assigned sessions
        $stmt = $this->pdo->prepare("
            SELECT es.*, e.title as event_title, sa.assigned_at
            FROM event_sessions es
            JOIN events e ON es.event_id = e.id
            JOIN session_assignments sa ON es.id = sa.session_id
            WHERE sa.user_id = ? AND sa.is_active = 1 
            AND es.status = 'active' AND e.is_active = 1
            ORDER BY es.start_datetime ASC
        ");
        $stmt->execute([$this->current_user_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Check if user can manage a specific event
     */
    public function canManageEvent($event_id) {
        if ($this->hasRole('super_admin')) {
            return true;
        }
        
        if ($this->hasRole('event_coordinator') || $this->hasRole('organizer')) {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM event_assignments 
                WHERE user_id = ? AND event_id = ? AND is_active = 1
            ");
            $stmt->execute([$this->current_user_id, $event_id]);
            return $stmt->fetchColumn() > 0;
        }
        
        return false;
    }
    
    /**
     * Check if user can manage a specific session
     */
    public function canManageSession($session_id) {
        if ($this->hasRole('super_admin')) {
            return true;
        }
        
        // Event coordinators can manage sessions in their assigned events
        if ($this->hasRole('event_coordinator')) {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM event_sessions es
                JOIN event_assignments ea ON es.event_id = ea.event_id
                WHERE ea.user_id = ? AND es.id = ? AND ea.is_active = 1
            ");
            $stmt->execute([$this->current_user_id, $session_id]);
            return $stmt->fetchColumn() > 0;
        }
        
        // Session moderators can manage their assigned sessions
        if ($this->hasRole('session_moderator')) {
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM session_assignments 
                WHERE user_id = ? AND session_id = ? AND is_active = 1
            ");
            $stmt->execute([$this->current_user_id, $session_id]);
            return $stmt->fetchColumn() > 0;
        }
        
        return false;
    }
    
    /**
     * Log dashboard access for audit purposes
     */
    public function logDashboardAccess($dashboard_name) {
        if (!$this->current_user_id) {
            return;
        }
        
        $highest_role = $this->getHighestRole();
        $role_name = $highest_role ? $highest_role['role_name'] : 'unknown';
        
        $stmt = $this->pdo->prepare("
            INSERT INTO dashboard_access_log 
            (user_id, role_name, dashboard_accessed, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?)
        ");
        $stmt->execute([
            $this->current_user_id,
            $role_name,
            $dashboard_name,
            $_SERVER['REMOTE_ADDR'] ?? null,
            $_SERVER['HTTP_USER_AGENT'] ?? null
        ]);
    }
    
    /**
     * Require specific permission or redirect
     */
    public function requirePermission($permission_name, $redirect_url = 'access_denied.php') {
        if (!$this->hasPermission($permission_name)) {
            header("Location: $redirect_url");
            exit();
        }
    }
    
    /**
     * Require specific role or redirect
     */
    public function requireRole($role_name, $redirect_url = 'access_denied.php') {
        if (!$this->hasRole($role_name)) {
            header("Location: $redirect_url");
            exit();
        }
    }
    
    /**
     * Get all available roles (for user management)
     */
    public function getAllRoles() {
        $stmt = $this->pdo->query("
            SELECT * FROM user_roles 
            ORDER BY hierarchy_level ASC
        ");
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Assign role to user
     */
    public function assignRole($user_id, $role_id, $expires_at = null) {
        if (!$this->hasPermission('user.assign_roles')) {
            throw new Exception('Insufficient permissions to assign roles');
        }
        
        $stmt = $this->pdo->prepare("
            INSERT INTO user_role_assignments 
            (user_id, role_id, assigned_by, expires_at) 
            VALUES (?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE 
            assigned_by = VALUES(assigned_by),
            expires_at = VALUES(expires_at),
            is_active = 1
        ");
        return $stmt->execute([$user_id, $role_id, $this->current_user_id, $expires_at]);
    }
    
    /**
     * Remove role from user
     */
    public function removeRole($user_id, $role_id) {
        if (!$this->hasPermission('user.assign_roles')) {
            throw new Exception('Insufficient permissions to remove roles');
        }
        
        $stmt = $this->pdo->prepare("
            UPDATE user_role_assignments 
            SET is_active = 0 
            WHERE user_id = ? AND role_id = ?
        ");
        return $stmt->execute([$user_id, $role_id]);
    }
}
