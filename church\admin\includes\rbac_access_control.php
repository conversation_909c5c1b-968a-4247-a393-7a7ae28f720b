<?php
/**
 * Role-Based Access Control (RBAC) System
 * Critical security implementation for dashboard access control
 */

class RBACAccessControl {
    private $pdo;
    private $user_id;
    private $user_roles = [];
    private $user_permissions = [];
    
    // Define role hierarchy (lower number = higher privilege)
    private $role_hierarchy = [
        'super_admin' => 1,
        'limited_admin' => 2,
        'event_coordinator' => 3,
        'session_moderator' => 4,
        'staff' => 5
    ];
    
    // Define page access permissions
    private $page_permissions = [
        // Super Admin Only Pages
        'setup_rbac_system.php' => ['super_admin'],
        'create_admin_users.php' => ['super_admin'],
        'system_test_dashboard.php' => ['super_admin'],
        'super_admin_dashboard.php' => ['super_admin'],
        'super_admin_navigation.php' => ['super_admin'],
        'initialize_rbac_database.php' => ['super_admin'],
        
        // Admin Pages (Super Admin + Limited Admin)
        'dashboard.php' => ['super_admin', 'limited_admin'],
        'members.php' => ['super_admin', 'limited_admin'],
        'add_member.php' => ['super_admin', 'limited_admin'],
        'edit_member.php' => ['super_admin', 'limited_admin'],
        'view_member.php' => ['super_admin', 'limited_admin'],
        'settings.php' => ['super_admin', 'limited_admin'],
        'profile.php' => ['super_admin', 'limited_admin'],
        
        // Event Management (Super Admin + Event Coordinators)
        'events.php' => ['super_admin', 'event_coordinator'],
        'event_sessions.php' => ['super_admin', 'event_coordinator'],
        'event_attendance.php' => ['super_admin', 'event_coordinator'],
        'event_coordinator_dashboard.php' => ['super_admin', 'event_coordinator'],
        
        // Session Management (Super Admin + Session Moderators)
        'session_moderator_dashboard.php' => ['super_admin', 'session_moderator'],
        'session_attendance.php' => ['super_admin', 'session_moderator'],
        
        // Staff Functions
        'staff_dashboard.php' => ['super_admin', 'staff'],
        
        // Public/Common Pages (accessible to all authenticated users)
        'login.php' => ['public'],
        'logout.php' => ['public'],
        'forgot_password.php' => ['public'],
        'reset_password.php' => ['public']
    ];
    
    public function __construct($pdo, $user_id = null) {
        $this->pdo = $pdo;
        $this->user_id = $user_id ?? $_SESSION['admin_id'] ?? null;
        
        if ($this->user_id) {
            $this->loadUserRoles();
        }
    }
    
    /**
     * Load user roles and permissions
     */
    private function loadUserRoles() {
        try {
            $stmt = $this->pdo->prepare("
                SELECT ur.role_name, ur.hierarchy_level, ur.role_display_name
                FROM user_role_assignments ura
                JOIN user_roles ur ON ura.role_id = ur.id
                WHERE ura.user_id = ? AND ura.is_active = 1
                AND (ura.expires_at IS NULL OR ura.expires_at > NOW())
                ORDER BY ur.hierarchy_level ASC
            ");
            $stmt->execute([$this->user_id]);
            $this->user_roles = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (PDOException $e) {
            error_log("RBAC Error loading user roles: " . $e->getMessage());
            $this->user_roles = [];
        }
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role_name) {
        foreach ($this->user_roles as $role) {
            if ($role['role_name'] === $role_name) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get user's highest privilege role
     */
    public function getPrimaryRole() {
        if (empty($this->user_roles)) {
            return null;
        }
        
        // Return role with lowest hierarchy level (highest privilege)
        $primary_role = $this->user_roles[0];
        return $primary_role['role_name'];
    }
    
    /**
     * Check if user can access a specific page
     */
    public function canAccessPage($page_name) {
        // Remove path and keep only filename
        $page_name = basename($page_name);
        
        // If page not in permissions array, deny access by default
        if (!isset($this->page_permissions[$page_name])) {
            return false;
        }
        
        $required_roles = $this->page_permissions[$page_name];
        
        // Public pages are accessible to all
        if (in_array('public', $required_roles)) {
            return true;
        }
        
        // Check if user has any of the required roles
        foreach ($this->user_roles as $user_role) {
            if (in_array($user_role['role_name'], $required_roles)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Enforce page access control
     */
    public function enforcePageAccess($page_name = null) {
        if (!$page_name) {
            $page_name = basename($_SERVER['PHP_SELF']);
        }
        
        if (!$this->canAccessPage($page_name)) {
            $this->redirectToAccessDenied();
        }
    }
    
    /**
     * Get appropriate dashboard for user's role
     */
    public function getDefaultDashboard() {
        $primary_role = $this->getPrimaryRole();
        
        switch ($primary_role) {
            case 'super_admin':
                return 'super_admin_dashboard.php';
            case 'limited_admin':
                return 'dashboard.php';
            case 'event_coordinator':
                return 'event_coordinator_dashboard.php';
            case 'session_moderator':
                return 'session_moderator_dashboard.php';
            case 'staff':
                return 'staff_dashboard.php';
            default:
                return 'access_denied.php';
        }
    }
    
    /**
     * Get sidebar navigation items based on user role
     */
    public function getSidebarItems() {
        $primary_role = $this->getPrimaryRole();
        
        switch ($primary_role) {
            case 'super_admin':
                return $this->getSuperAdminSidebar();
            case 'limited_admin':
                return $this->getLimitedAdminSidebar();
            case 'event_coordinator':
                return $this->getEventCoordinatorSidebar();
            case 'session_moderator':
                return $this->getSessionModeratorSidebar();
            case 'staff':
                return $this->getStaffSidebar();
            default:
                return [];
        }
    }
    
    /**
     * Super Admin - Full Access Sidebar
     */
    private function getSuperAdminSidebar() {
        return [
            'dashboard' => [
                'title' => 'Dashboard',
                'url' => 'super_admin_dashboard.php',
                'icon' => 'bi-speedometer2'
            ],
            'members' => [
                'title' => 'Member Management',
                'icon' => 'bi-people',
                'items' => [
                    ['title' => 'Members', 'url' => 'members.php', 'icon' => 'bi-people'],
                    ['title' => 'Add Member', 'url' => 'add_member.php', 'icon' => 'bi-person-plus'],
                    ['title' => 'Family Management', 'url' => 'family_management.php', 'icon' => 'bi-house'],
                ]
            ],
            'events' => [
                'title' => 'Events Management',
                'icon' => 'bi-calendar-event',
                'items' => [
                    ['title' => 'Events', 'url' => 'events.php', 'icon' => 'bi-calendar'],
                    ['title' => 'Event Sessions', 'url' => 'event_sessions.php', 'icon' => 'bi-calendar-week'],
                    ['title' => 'Event Attendance', 'url' => 'event_attendance.php', 'icon' => 'bi-check-square'],
                ]
            ],
            'role_dashboards' => [
                'title' => 'Role Dashboards',
                'icon' => 'bi-speedometer2',
                'items' => [
                    ['title' => 'Event Coordinator', 'url' => 'event_coordinator_dashboard.php', 'icon' => 'bi-calendar-event'],
                    ['title' => 'Session Moderator', 'url' => 'session_moderator_dashboard.php', 'icon' => 'bi-person-video2'],
                    ['title' => 'Staff Dashboard', 'url' => 'staff_dashboard.php', 'icon' => 'bi-person-check'],
                ]
            ],
            'system_admin' => [
                'title' => 'System Administration',
                'icon' => 'bi-gear-wide-connected',
                'items' => [
                    ['title' => 'RBAC Management', 'url' => 'setup_rbac_system.php', 'icon' => 'bi-shield-check'],
                    ['title' => 'Create Admin Users', 'url' => 'create_admin_users.php', 'icon' => 'bi-person-plus'],
                    ['title' => 'System Testing', 'url' => 'system_test_dashboard.php', 'icon' => 'bi-bug'],
                    ['title' => 'Navigation Guide', 'url' => 'super_admin_navigation.php', 'icon' => 'bi-compass'],
                ]
            ],
            'settings' => [
                'title' => 'Settings',
                'url' => 'settings.php',
                'icon' => 'bi-gear'
            ]
        ];
    }
    
    /**
     * Limited Admin - Basic Admin Functions Only
     */
    private function getLimitedAdminSidebar() {
        return [
            'dashboard' => [
                'title' => 'Dashboard',
                'url' => 'dashboard.php',
                'icon' => 'bi-speedometer2'
            ],
            'members' => [
                'title' => 'Member Management',
                'icon' => 'bi-people',
                'items' => [
                    ['title' => 'Members', 'url' => 'members.php', 'icon' => 'bi-people'],
                    ['title' => 'Add Member', 'url' => 'add_member.php', 'icon' => 'bi-person-plus'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Event Coordinator - Event Management Only
     */
    private function getEventCoordinatorSidebar() {
        return [
            'dashboard' => [
                'title' => 'Event Coordinator Dashboard',
                'url' => 'event_coordinator_dashboard.php',
                'icon' => 'bi-calendar-event'
            ],
            'events' => [
                'title' => 'My Events',
                'icon' => 'bi-calendar',
                'items' => [
                    ['title' => 'Assigned Events', 'url' => 'events.php', 'icon' => 'bi-calendar'],
                    ['title' => 'Event Sessions', 'url' => 'event_sessions.php', 'icon' => 'bi-calendar-week'],
                    ['title' => 'Event Attendance', 'url' => 'event_attendance.php', 'icon' => 'bi-check-square'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Session Moderator - Session Management Only
     */
    private function getSessionModeratorSidebar() {
        return [
            'dashboard' => [
                'title' => 'Session Moderator Dashboard',
                'url' => 'session_moderator_dashboard.php',
                'icon' => 'bi-person-video2'
            ],
            'sessions' => [
                'title' => 'My Sessions',
                'icon' => 'bi-calendar-week',
                'items' => [
                    ['title' => 'Assigned Sessions', 'url' => 'session_moderator_dashboard.php', 'icon' => 'bi-calendar-week'],
                    ['title' => 'Session Attendance', 'url' => 'session_attendance.php', 'icon' => 'bi-check-square'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Staff - Basic Check-in Only
     */
    private function getStaffSidebar() {
        return [
            'dashboard' => [
                'title' => 'Staff Dashboard',
                'url' => 'staff_dashboard.php',
                'icon' => 'bi-person-check'
            ],
            'checkin' => [
                'title' => 'Check-in Functions',
                'icon' => 'bi-check-circle',
                'items' => [
                    ['title' => 'Member Check-in', 'url' => 'staff_dashboard.php', 'icon' => 'bi-check-circle'],
                ]
            ],
            'profile' => [
                'title' => 'Profile',
                'url' => 'profile.php',
                'icon' => 'bi-person-circle'
            ]
        ];
    }
    
    /**
     * Redirect to access denied page
     */
    private function redirectToAccessDenied() {
        header("Location: access_denied.php?reason=insufficient_permissions");
        exit();
    }
    
    /**
     * Get all user roles for display
     */
    public function getUserRoles() {
        return $this->user_roles;
    }
    
    /**
     * Check if user is super admin
     */
    public function isSuperAdmin() {
        return $this->hasRole('super_admin');
    }
    
    /**
     * Check if user has admin privileges (super_admin or limited_admin)
     */
    public function isAdmin() {
        return $this->hasRole('super_admin') || $this->hasRole('limited_admin');
    }
}

// Global RBAC instance
if (!isset($rbac)) {
    $rbac = new RBACAccessControl($pdo);
}
?>
