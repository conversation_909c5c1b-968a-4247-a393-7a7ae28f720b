# Bulk Attendance Management System - User Guide

## Overview

The Bulk Attendance Management System is designed to efficiently handle large events with 2000+ attendees. Instead of manually marking each attendee one by one, you can now use powerful bulk operations to manage attendance in seconds.

## Key Features

### 🚀 Event-Level Bulk Operations
- **Bulk Select**: Select all or none attendees at once
- **Bulk Mark**: Mark selected attendees as attended/not attended
- **Global Operations**: Mark ALL attendees (across all pages) as attended/not attended/clear
- **Smart Attendance**: Auto-mark event attendance based on session attendance
- **Pagination**: View 25-500 attendees per page
- **Search & Filter**: Find specific attendees quickly
- **CSV Export/Import**: Bulk update attendance via spreadsheet

### 📊 Session-Level Bulk Operations
- **Session Bulk Select**: Select all or none session attendees
- **Session Bulk Mark**: Mark selected as attended/no-show
- **Session Global Operations**: Mark ALL session registrants as attended/no-show/reset
- **Cross-Session Operations**: Apply attendance across multiple sessions

## Getting Started

### For Events with 2000+ Attendees

1. **Navigate to Event Attendance**
   - Go to Events → Select Event → View Attendance
   - You'll see attendees paginated (50 per page by default)

2. **Use Search to Find Specific Groups**
   - Search by name or email to find specific attendees
   - Adjust "Per Page" to show more attendees (up to 500)

3. **Bulk Operations Workflow**
   - Use "Select All" to select all attendees on current page
   - Use bulk buttons to mark selected attendees
   - Use "Global Operations" to affect ALL attendees (not just current page)

### For Events with Sessions

1. **Smart Attendance Approach**
   - If your event has sessions, use "Smart Attendance"
   - Set minimum sessions required (e.g., attended at least 2 sessions)
   - Click "Smart Mark" to auto-mark event attendance

2. **Session-by-Session Management**
   - Go to Event Sessions → Select Session → Manage Attendance
   - Use session-level bulk operations for each session
   - Mark attendees as attended/no-show for specific sessions

## Detailed Feature Guide

### Event-Level Features

#### Bulk Selection Controls
```
[Select All] [Select None]    0 selected
[Mark Selected Attended] [Mark Selected Not Attended]
```
- **Select All**: Selects all attendees on current page
- **Select None**: Deselects all attendees
- **Selected Counter**: Shows how many attendees are selected
- **Bulk Action Buttons**: Only enabled when attendees are selected

#### Global Bulk Operations
```
⚠️ Global Bulk Operations
These operations affect ALL 2,000 attendees, not just those shown on this page.
[Mark All Attended] [Mark All Not Attended] [Clear All]
```
- **Mark All Attended**: Marks ALL event attendees as attended
- **Mark All Not Attended**: Marks ALL event attendees as not attended  
- **Clear All**: Removes attendance marks for ALL attendees

#### Smart Attendance
```
💡 Smart Attendance
This event has 5 session(s). Auto-mark event attendance based on session attendance.
Min Sessions: [2] [Smart Mark]
```
- **Min Sessions**: Minimum sessions someone must attend to be marked as attending the event
- **Smart Mark**: Automatically marks event attendance based on session attendance

#### Search & Pagination
```
Search Name/Email: [search box]  Per Page: [50] [Search] [Clear]
Showing 1 to 50 of 2,000 attendees
[Previous] [1] [2] [3] [4] [5] [Next]
```
- **Search**: Find attendees by name or email
- **Per Page**: Show 25, 50, 100, 200, or 500 attendees per page
- **Pagination**: Navigate through large attendee lists

#### CSV Export/Import
```
[Export CSV] [Import CSV]
```
- **Export CSV**: Download attendance data as spreadsheet
- **Import CSV**: Upload modified attendance data from spreadsheet

### Session-Level Features

#### Session Bulk Operations
```
⚡ Bulk Operations
[Select All] [Select None]    0 selected
[Mark Selected Attended] [Mark Selected No-Show]

Global Operations affect ALL 300 registered attendees:
[Mark All Attended] [Mark All No-Show] [Reset All]
```
- **Session Selection**: Select attendees for specific session
- **Mark Attended/No-Show**: Update session attendance status
- **Reset All**: Reset all session attendance back to "registered"

## Workflow Recommendations

### For Large Events (2000+ Attendees)

#### Pre-Event Setup
1. **Set Default Status**: Consider marking all as "attended" initially if most people who RSVP actually show up
2. **Prepare Staff**: Assign staff members to different sections or entry points
3. **Test System**: Practice with bulk operations before the event

#### During Event
1. **Entry Point Strategy**:
   - Use tablets/phones at entry points for quick check-in
   - Assign 1 staff member per 200-300 attendees
   - Use search function to quickly find specific attendees

2. **Real-Time Updates**:
   - Multiple staff can work simultaneously on different pages
   - Changes are saved immediately
   - Use pagination to divide work among staff

#### Post-Event
1. **Bulk Corrections**: Use bulk operations to fix any errors
2. **Smart Attendance**: If event had sessions, use smart attendance to auto-mark based on session participation
3. **Export Data**: Export final attendance for reporting

### For Multi-Session Events

#### Session Management Strategy
1. **Session-by-Session**: Mark attendance for each session individually
2. **Cross-Session Patterns**: Use bulk operations to apply similar attendance patterns
3. **Smart Integration**: Use smart attendance to automatically determine overall event attendance

#### Staff Coordination
1. **Location-Based**: Assign staff to specific session locations
2. **Real-Time Sync**: All changes sync immediately across devices
3. **Conflict Detection**: System prevents impossible attendance patterns

## Troubleshooting

### Common Issues

**Q: I selected attendees but bulk buttons are disabled**
A: Make sure you've actually selected attendees using the checkboxes, not just clicked "Select All" without attendees visible.

**Q: Global operations aren't working**
A: Global operations affect ALL attendees, not just those on current page. Confirm you want to affect all attendees before proceeding.

**Q: CSV import failed**
A: Ensure your CSV has the correct headers: "RSVP ID" and "Actually Attended". RSVP IDs must match the exported format.

**Q: Smart attendance isn't marking anyone**
A: Check that the event has sessions and that session attendance has been recorded. Smart attendance only works if people have attended sessions.

### Performance Tips

1. **Use Pagination**: Don't try to load all 2000+ attendees on one page
2. **Use Search**: Filter to specific groups before bulk operations
3. **Batch Operations**: Process attendees in logical groups (e.g., by registration date, member type)
4. **Regular Saves**: Bulk operations save automatically, but individual changes need to be saved

## Security & Data Integrity

- All bulk operations require confirmation before execution
- Changes are logged for audit purposes
- Database transactions ensure data consistency
- Backup recommendations: Export CSV before major bulk operations

## Support

For technical issues or questions about bulk attendance management:
1. Check this user guide first
2. Test with a small group before applying to all attendees
3. Use CSV export as backup before major operations
4. Contact system administrator for database-level issues

---

**Remember**: Bulk operations are powerful but irreversible. Always confirm your selections before executing bulk operations on large datasets.
