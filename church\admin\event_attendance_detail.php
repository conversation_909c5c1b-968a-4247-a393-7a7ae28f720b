<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

$message = '';
$error = '';

// Get event ID from URL
$event_id = isset($_GET['event_id']) ? (int)$_GET['event_id'] : 0;

if (!$event_id) {
    header("Location: event_attendance.php");
    exit();
}

// Ensure both RSVP tables have actually_attended column
try {
    $conn->exec("ALTER TABLE event_rsvps ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL");
} catch (PDOException $e) {
    // Column might already exist, ignore error
}

try {
    $conn->exec("ALTER TABLE event_rsvps_guests ADD COLUMN actually_attended TINYINT(1) DEFAULT NULL");
} catch (PDOException $e) {
    // Column might already exist, ignore error
}

// Handle attendance updates and bulk operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    try {
        $conn->beginTransaction();

        if ($_POST['action'] === 'update_attendance') {
            $attendance_data = $_POST['attendance'] ?? [];

            // Update attendance for each user
            foreach ($attendance_data as $rsvp_id => $attended) {
                $actually_attended = ($attended === '1') ? 1 : 0;

                // Check if this is a guest RSVP (prefixed with 'guest_')
                if (strpos($rsvp_id, 'guest_') === 0) {
                    $guest_id = str_replace('guest_', '', $rsvp_id);
                    $stmt = $conn->prepare("
                        UPDATE event_rsvps_guests
                        SET actually_attended = ?
                        WHERE id = ? AND event_id = ?
                    ");
                    $stmt->execute([$actually_attended, $guest_id, $event_id]);
                } else {
                    // Regular member RSVP
                    $stmt = $conn->prepare("
                        UPDATE event_rsvps
                        SET actually_attended = ?
                        WHERE id = ? AND event_id = ?
                    ");
                    $stmt->execute([$actually_attended, $rsvp_id, $event_id]);
                }
            }
            $message = "Attendance records updated successfully!";

        } elseif ($_POST['action'] === 'bulk_mark_attended') {
            // Mark all attendees as attended
            $stmt = $conn->prepare("UPDATE event_rsvps SET actually_attended = 1 WHERE event_id = ? AND status = 'attending'");
            $stmt->execute([$event_id]);
            $member_count = $stmt->rowCount();

            $stmt = $conn->prepare("UPDATE event_rsvps_guests SET actually_attended = 1 WHERE event_id = ? AND status = 'attending'");
            $stmt->execute([$event_id]);
            $guest_count = $stmt->rowCount();

            $total_marked = $member_count + $guest_count;
            $message = "Successfully marked {$total_marked} attendees as attended ({$member_count} members, {$guest_count} guests).";

        } elseif ($_POST['action'] === 'bulk_mark_not_attended') {
            // Mark all attendees as not attended
            $stmt = $conn->prepare("UPDATE event_rsvps SET actually_attended = 0 WHERE event_id = ? AND status = 'attending'");
            $stmt->execute([$event_id]);
            $member_count = $stmt->rowCount();

            $stmt = $conn->prepare("UPDATE event_rsvps_guests SET actually_attended = 0 WHERE event_id = ? AND status = 'attending'");
            $stmt->execute([$event_id]);
            $guest_count = $stmt->rowCount();

            $total_marked = $member_count + $guest_count;
            $message = "Successfully marked {$total_marked} attendees as not attended ({$member_count} members, {$guest_count} guests).";

        } elseif ($_POST['action'] === 'bulk_clear_attendance') {
            // Clear all attendance marks
            $stmt = $conn->prepare("UPDATE event_rsvps SET actually_attended = NULL WHERE event_id = ? AND status = 'attending'");
            $stmt->execute([$event_id]);
            $member_count = $stmt->rowCount();

            $stmt = $conn->prepare("UPDATE event_rsvps_guests SET actually_attended = NULL WHERE event_id = ? AND status = 'attending'");
            $stmt->execute([$event_id]);
            $guest_count = $stmt->rowCount();

            $total_cleared = $member_count + $guest_count;
            $message = "Successfully cleared attendance marks for {$total_cleared} attendees ({$member_count} members, {$guest_count} guests).";

        } elseif ($_POST['action'] === 'bulk_selected_attended') {
            // Mark selected attendees as attended
            $selected_ids = $_POST['selected_attendees'] ?? [];
            $marked_count = 0;

            foreach ($selected_ids as $rsvp_id) {
                if (strpos($rsvp_id, 'guest_') === 0) {
                    $guest_id = str_replace('guest_', '', $rsvp_id);
                    $stmt = $conn->prepare("UPDATE event_rsvps_guests SET actually_attended = 1 WHERE id = ? AND event_id = ?");
                    $stmt->execute([$guest_id, $event_id]);
                } else {
                    $stmt = $conn->prepare("UPDATE event_rsvps SET actually_attended = 1 WHERE id = ? AND event_id = ?");
                    $stmt->execute([$rsvp_id, $event_id]);
                }
                $marked_count += $stmt->rowCount();
            }

            $message = "Successfully marked {$marked_count} selected attendees as attended.";

            // If preserve_selections is set, redirect with selections preserved
            if (isset($_POST['preserve_selections'])) {
                $selected_ids = $_POST['selected_attendees'] ?? [];
                $redirect_url = "?event_id=" . $event_id . "&preserve_selections=" . urlencode(json_encode($selected_ids));
                header("Location: " . $redirect_url);
                exit();
            }

        } elseif ($_POST['action'] === 'bulk_selected_not_attended') {
            // Mark selected attendees as not attended
            $selected_ids = $_POST['selected_attendees'] ?? [];
            $marked_count = 0;

            foreach ($selected_ids as $rsvp_id) {
                if (strpos($rsvp_id, 'guest_') === 0) {
                    $guest_id = str_replace('guest_', '', $rsvp_id);
                    $stmt = $conn->prepare("UPDATE event_rsvps_guests SET actually_attended = 0 WHERE id = ? AND event_id = ?");
                    $stmt->execute([$guest_id, $event_id]);
                } else {
                    $stmt = $conn->prepare("UPDATE event_rsvps SET actually_attended = 0 WHERE id = ? AND event_id = ?");
                    $stmt->execute([$rsvp_id, $event_id]);
                }
                $marked_count += $stmt->rowCount();
            }

            $message = "Successfully marked {$marked_count} selected attendees as not attended.";

            // If preserve_selections is set, redirect with selections preserved
            if (isset($_POST['preserve_selections'])) {
                $selected_ids = $_POST['selected_attendees'] ?? [];
                $redirect_url = "?event_id=" . $event_id . "&preserve_selections=" . urlencode(json_encode($selected_ids));
                header("Location: " . $redirect_url);
                exit();
            }

        } elseif ($_POST['action'] === 'import_csv') {
            // Handle CSV import
            if (isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
                $csv_file = $_FILES['csv_file']['tmp_name'];
                $update_existing = isset($_POST['update_existing']);

                try {
                    $handle = fopen($csv_file, 'r');
                    if ($handle === false) {
                        throw new Exception("Could not open CSV file.");
                    }

                    // Read header row
                    $headers = fgetcsv($handle);
                    if (!$headers || !in_array('RSVP ID', $headers) || !in_array('Actually Attended', $headers)) {
                        throw new Exception("CSV must have 'RSVP ID' and 'Actually Attended' columns.");
                    }

                    $rsvp_id_index = array_search('RSVP ID', $headers);
                    $attended_index = array_search('Actually Attended', $headers);

                    $imported_count = 0;
                    $error_count = 0;
                    $errors = [];

                    while (($row = fgetcsv($handle)) !== false) {
                        if (count($row) <= max($rsvp_id_index, $attended_index)) {
                            continue; // Skip incomplete rows
                        }

                        $rsvp_id = trim($row[$rsvp_id_index]);
                        $attended_value = trim($row[$attended_index]);

                        if (empty($rsvp_id)) {
                            continue; // Skip empty RSVP IDs
                        }

                        // Convert attendance value
                        $actually_attended = null;
                        if ($attended_value === '1' || strtolower($attended_value) === 'attended') {
                            $actually_attended = 1;
                        } elseif ($attended_value === '0' || strtolower($attended_value) === 'not attended' || strtolower($attended_value) === 'did not attend') {
                            $actually_attended = 0;
                        }

                        try {
                            // Check if this is a guest RSVP
                            if (strpos($rsvp_id, 'guest_') === 0) {
                                $guest_id = str_replace('guest_', '', $rsvp_id);
                                $stmt = $conn->prepare("
                                    UPDATE event_rsvps_guests
                                    SET actually_attended = ?
                                    WHERE id = ? AND event_id = ?
                                ");
                                $stmt->execute([$actually_attended, $guest_id, $event_id]);
                            } else {
                                // Regular member RSVP
                                $stmt = $conn->prepare("
                                    UPDATE event_rsvps
                                    SET actually_attended = ?
                                    WHERE id = ? AND event_id = ?
                                ");
                                $stmt->execute([$actually_attended, $rsvp_id, $event_id]);
                            }

                            if ($stmt->rowCount() > 0) {
                                $imported_count++;
                            } else {
                                $errors[] = "RSVP ID '{$rsvp_id}' not found or no changes made.";
                                $error_count++;
                            }

                        } catch (PDOException $e) {
                            $errors[] = "Error updating RSVP ID '{$rsvp_id}': " . $e->getMessage();
                            $error_count++;
                        }
                    }

                    fclose($handle);

                    if ($imported_count > 0) {
                        $message = "CSV import completed: {$imported_count} records updated successfully.";
                        if ($error_count > 0) {
                            $message .= " {$error_count} errors occurred.";
                        }
                    } else {
                        $error = "No records were updated. Please check your CSV format and data.";
                        if (!empty($errors)) {
                            $error .= " Errors: " . implode('; ', array_slice($errors, 0, 5));
                        }
                    }

                } catch (Exception $e) {
                    $error = "CSV import failed: " . $e->getMessage();
                }
            } else {
                $error = "Please select a valid CSV file to import.";
            }

        } elseif ($_POST['action'] === 'smart_mark_from_sessions') {
            // Smart attendance marking based on session attendance
            $min_sessions = isset($_POST['min_sessions']) ? (int)$_POST['min_sessions'] : 1;
            $marked_count = 0;

            // Get all sessions for this event
            $sessions_stmt = $conn->prepare("SELECT id FROM event_sessions WHERE event_id = ?");
            $sessions_stmt->execute([$event_id]);
            $session_ids = $sessions_stmt->fetchAll(PDO::FETCH_COLUMN);

            if (!empty($session_ids)) {
                $session_placeholders = str_repeat('?,', count($session_ids) - 1) . '?';

                // Mark members as attended if they attended minimum number of sessions
                $member_stmt = $conn->prepare("
                    UPDATE event_rsvps er
                    SET actually_attended = 1
                    WHERE er.event_id = ? AND er.status = 'attending'
                    AND er.user_id IN (
                        SELECT sa.member_id
                        FROM session_attendance sa
                        WHERE sa.session_id IN ($session_placeholders)
                        AND sa.attendance_status = 'attended'
                        AND sa.member_id IS NOT NULL
                        GROUP BY sa.member_id
                        HAVING COUNT(*) >= ?
                    )
                ");
                $member_params = array_merge([$event_id], $session_ids, [$min_sessions]);
                $member_stmt->execute($member_params);
                $member_marked = $member_stmt->rowCount();

                // Mark guests as attended if they attended minimum number of sessions
                $guest_stmt = $conn->prepare("
                    UPDATE event_rsvps_guests erg
                    SET actually_attended = 1
                    WHERE erg.event_id = ? AND erg.status = 'attending'
                    AND (erg.guest_name, erg.guest_email) IN (
                        SELECT sa.guest_name, sa.guest_email
                        FROM session_attendance sa
                        WHERE sa.session_id IN ($session_placeholders)
                        AND sa.attendance_status = 'attended'
                        AND sa.guest_name IS NOT NULL
                        AND sa.guest_email IS NOT NULL
                        GROUP BY sa.guest_name, sa.guest_email
                        HAVING COUNT(*) >= ?
                    )
                ");
                $guest_params = array_merge([$event_id], $session_ids, [$min_sessions]);
                $guest_stmt->execute($guest_params);
                $guest_marked = $guest_stmt->rowCount();

                $marked_count = $member_marked + $guest_marked;
                $message = "Smart attendance marking completed: {$marked_count} attendees marked as attended based on session attendance ({$member_marked} members, {$guest_marked} guests). Minimum sessions required: {$min_sessions}.";
            } else {
                $message = "No sessions found for this event. Cannot perform smart attendance marking.";
            }
        }

        $conn->commit();

    } catch (Exception $e) {
        $conn->rollBack();
        $error = "Error updating attendance: " . $e->getMessage();
    }
}

// Handle CSV export
if (isset($_GET['action']) && $_GET['action'] === 'export_csv') {
    try {
        // Get all attendees for export
        $export_member_query = "
            SELECT
                er.id as rsvp_id,
                m.full_name,
                m.email,
                m.phone_number,
                'member' as attendee_type,
                er.status,
                er.notes,
                er.actually_attended,
                er.created_at as rsvp_date
            FROM event_rsvps er
            JOIN members m ON er.user_id = m.id
            WHERE er.event_id = ? AND er.status = 'attending'
            ORDER BY m.full_name ASC
        ";

        $export_member_stmt = $conn->prepare($export_member_query);
        $export_member_stmt->execute([$event_id]);
        $export_members = $export_member_stmt->fetchAll(PDO::FETCH_ASSOC);

        $export_guest_query = "
            SELECT
                CONCAT('guest_', erg.id) as rsvp_id,
                erg.guest_name as full_name,
                erg.guest_email as email,
                erg.guest_phone as phone_number,
                'guest' as attendee_type,
                erg.status,
                erg.special_requirements as notes,
                erg.actually_attended,
                erg.created_at as rsvp_date
            FROM event_rsvps_guests erg
            WHERE erg.event_id = ? AND erg.status = 'attending'
            ORDER BY erg.guest_name ASC
        ";

        $export_guest_stmt = $conn->prepare($export_guest_query);
        $export_guest_stmt->execute([$event_id]);
        $export_guests = $export_guest_stmt->fetchAll(PDO::FETCH_ASSOC);

        $all_export_attendees = array_merge($export_members, $export_guests);

        // Set headers for CSV download
        $filename = 'event_' . $event_id . '_attendance_' . date('Y-m-d_H-i-s') . '.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Create CSV output
        $output = fopen('php://output', 'w');

        // CSV headers
        fputcsv($output, [
            'RSVP ID',
            'Full Name',
            'Email',
            'Phone',
            'Type',
            'RSVP Status',
            'Notes',
            'Actually Attended',
            'Attendance Status',
            'RSVP Date'
        ]);

        // CSV data
        foreach ($all_export_attendees as $attendee) {
            $attendance_status = '';
            if ($attendee['actually_attended'] === null) {
                $attendance_status = 'Not Marked';
            } elseif ($attendee['actually_attended'] == 1) {
                $attendance_status = 'Attended';
            } else {
                $attendance_status = 'Did Not Attend';
            }

            fputcsv($output, [
                $attendee['rsvp_id'],
                $attendee['full_name'],
                $attendee['email'],
                $attendee['phone_number'] ?: '',
                ucfirst($attendee['attendee_type']),
                ucfirst($attendee['status']),
                $attendee['notes'] ?: '',
                $attendee['actually_attended'] ?: '0',
                $attendance_status,
                date('Y-m-d H:i:s', strtotime($attendee['rsvp_date']))
            ]);
        }

        fclose($output);
        exit();

    } catch (Exception $e) {
        $error = "Error exporting CSV: " . $e->getMessage();
    }
}

// Get event details
$event_stmt = $conn->prepare("
    SELECT id, title, event_date, location, description, max_attendees
    FROM events
    WHERE id = ?
");
$event_stmt->execute([$event_id]);
$event = $event_stmt->fetch(PDO::FETCH_ASSOC);

if (!$event) {
    header("Location: event_attendance.php");
    exit();
}

// Pagination and search parameters
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = isset($_GET['limit']) ? max(10, min(500, (int)$_GET['limit'])) : 50; // Default 50 per page, max 500
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$filter_status = isset($_GET['filter_status']) ? $_GET['filter_status'] : '';
$filter_type = isset($_GET['filter_type']) ? $_GET['filter_type'] : '';

// Build search conditions
$search_conditions = [];
$search_params = [$event_id];

if (!empty($search)) {
    $search_conditions[] = "(m.full_name LIKE ? OR m.email LIKE ? OR erg.guest_name LIKE ? OR erg.guest_email LIKE ?)";
    $search_term = "%{$search}%";
    $search_params = array_merge($search_params, [$search_term, $search_term, $search_term, $search_term]);
}

$search_where = !empty($search_conditions) ? 'AND ' . implode(' AND ', $search_conditions) : '';

// Calculate offset for pagination
$offset = ($page - 1) * $limit;

// Get total count of attendees for pagination (without search filters)
$total_count_query = "
    SELECT
        (SELECT COUNT(*) FROM event_rsvps er JOIN members m ON er.user_id = m.id WHERE er.event_id = ? AND er.status = 'attending') +
        (SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = ? AND erg.status = 'attending') as total_count
";
$total_stmt = $conn->prepare($total_count_query);
$total_stmt->execute([$event_id, $event_id]);
$total_attendees = $total_stmt->fetchColumn();

// Get filtered count for search results
if (!empty($search)) {
    // Count members matching search
    $member_count_query = "SELECT COUNT(*) FROM event_rsvps er JOIN members m ON er.user_id = m.id WHERE er.event_id = ? AND er.status = 'attending' AND (m.full_name LIKE ? OR m.email LIKE ?)";
    $member_count_stmt = $conn->prepare($member_count_query);
    $member_count_stmt->execute([$event_id, $search_term, $search_term]);
    $member_count = $member_count_stmt->fetchColumn();

    // Count guests matching search
    $guest_count_query = "SELECT COUNT(*) FROM event_rsvps_guests erg WHERE erg.event_id = ? AND erg.status = 'attending' AND (erg.guest_name LIKE ? OR erg.guest_email LIKE ?)";
    $guest_count_stmt = $conn->prepare($guest_count_query);
    $guest_count_stmt->execute([$event_id, $search_term, $search_term]);
    $guest_count = $guest_count_stmt->fetchColumn();

    $filtered_attendees = $member_count + $guest_count;
} else {
    $filtered_attendees = $total_attendees;
}

// Calculate pagination info
$total_pages = ceil($filtered_attendees / $limit);
$has_previous = $page > 1;
$has_next = $page < $total_pages;

// Get paginated attendees with search
// Fetch member RSVPs
if (!empty($search)) {
    $member_query = "
        SELECT
            er.id as rsvp_id,
            er.user_id,
            er.status,
            er.notes,
            er.actually_attended,
            er.created_at as rsvp_date,
            m.full_name,
            m.email,
            m.phone_number,
            'member' as attendee_type
        FROM event_rsvps er
        JOIN members m ON er.user_id = m.id
        WHERE er.event_id = ? AND er.status = 'attending' AND (m.full_name LIKE ? OR m.email LIKE ?)
        ORDER BY m.full_name ASC
    ";
    $member_stmt = $conn->prepare($member_query);
    $member_stmt->execute([$event_id, $search_term, $search_term]);
} else {
    $member_query = "
        SELECT
            er.id as rsvp_id,
            er.user_id,
            er.status,
            er.notes,
            er.actually_attended,
            er.created_at as rsvp_date,
            m.full_name,
            m.email,
            m.phone_number,
            'member' as attendee_type
        FROM event_rsvps er
        JOIN members m ON er.user_id = m.id
        WHERE er.event_id = ? AND er.status = 'attending'
        ORDER BY m.full_name ASC
    ";
    $member_stmt = $conn->prepare($member_query);
    $member_stmt->execute([$event_id]);
}
$member_attendees = $member_stmt->fetchAll(PDO::FETCH_ASSOC);

// Fetch guest RSVPs
if (!empty($search)) {
    $guest_query = "
        SELECT
            CONCAT('guest_', erg.id) as rsvp_id,
            NULL as user_id,
            erg.status,
            erg.special_requirements as notes,
            erg.actually_attended,
            erg.created_at as rsvp_date,
            erg.guest_name as full_name,
            erg.guest_email as email,
            erg.guest_phone as phone_number,
            'guest' as attendee_type
        FROM event_rsvps_guests erg
        WHERE erg.event_id = ? AND erg.status = 'attending' AND (erg.guest_name LIKE ? OR erg.guest_email LIKE ?)
        ORDER BY erg.guest_name ASC
    ";
    $guest_stmt = $conn->prepare($guest_query);
    $guest_stmt->execute([$event_id, $search_term, $search_term]);
} else {
    $guest_query = "
        SELECT
            CONCAT('guest_', erg.id) as rsvp_id,
            NULL as user_id,
            erg.status,
            erg.special_requirements as notes,
            erg.actually_attended,
            erg.created_at as rsvp_date,
            erg.guest_name as full_name,
            erg.guest_email as email,
            erg.guest_phone as phone_number,
            'guest' as attendee_type
        FROM event_rsvps_guests erg
        WHERE erg.event_id = ? AND erg.status = 'attending'
        ORDER BY erg.guest_name ASC
    ";
    $guest_stmt = $conn->prepare($guest_query);
    $guest_stmt->execute([$event_id]);
}
$guest_attendees = $guest_stmt->fetchAll(PDO::FETCH_ASSOC);

// Merge and sort all attendees
$all_attendees = array_merge($member_attendees, $guest_attendees);
usort($all_attendees, function($a, $b) {
    return strcasecmp($a['full_name'], $b['full_name']);
});

// Apply pagination to the merged results
$attendees = array_slice($all_attendees, $offset, $limit);

// Set page variables
$page_title = "Event Attendance - " . $event['title'];
$page_header = "Mark Attendance";
$page_description = "Mark actual attendance for " . $event['title'];

// Include header
include 'includes/header.php';
?>

<div class="container-fluid">
    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo htmlspecialchars($error); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Event Details Card -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-calendar-event"></i> Event Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <h4><?php echo htmlspecialchars($event['title']); ?></h4>
                            <p class="text-muted mb-2">
                                <i class="bi bi-calendar"></i> 
                                <?php 
                                $event_date = new DateTime($event['event_date']);
                                echo $event_date->format('l, F j, Y \a\t g:i A');
                                ?>
                            </p>
                            <p class="text-muted mb-2">
                                <i class="bi bi-geo-alt"></i> 
                                <?php echo htmlspecialchars($event['location'] ?? 'Location TBD'); ?>
                            </p>
                            <?php if ($event['description']): ?>
                                <p class="mb-0"><?php echo nl2br(htmlspecialchars($event['description'])); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="d-flex flex-column gap-2">
                                <a href="event_attendance.php" class="btn btn-outline-secondary">
                                    <i class="bi bi-arrow-left"></i> Back to Events
                                </a>
                                <a href="events.php" class="btn btn-outline-primary">
                                    <i class="bi bi-pencil"></i> Edit Event
                                </a>
                                <a href="?event_id=<?php echo $event_id; ?>&action=export_csv" class="btn btn-outline-success">
                                    <i class="bi bi-download"></i> Export CSV
                                </a>
                                <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#importCsvModal">
                                    <i class="bi bi-upload"></i> Import CSV
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search and Filter Controls -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-search"></i> Search & Filter Attendees
                    </h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="" class="row g-3">
                        <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">

                        <div class="col-md-4">
                            <label for="search" class="form-label">Search Name/Email</label>
                            <input type="text" class="form-control" id="search" name="search"
                                   value="<?php echo htmlspecialchars($search); ?>"
                                   placeholder="Search by name or email...">
                        </div>

                        <div class="col-md-3">
                            <label for="limit" class="form-label">Per Page</label>
                            <select class="form-select" id="limit" name="limit">
                                <option value="25" <?php echo $limit == 25 ? 'selected' : ''; ?>>25</option>
                                <option value="50" <?php echo $limit == 50 ? 'selected' : ''; ?>>50</option>
                                <option value="100" <?php echo $limit == 100 ? 'selected' : ''; ?>>100</option>
                                <option value="200" <?php echo $limit == 200 ? 'selected' : ''; ?>>200</option>
                                <option value="500" <?php echo $limit == 500 ? 'selected' : ''; ?>>500</option>
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-flex gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-search"></i> Search
                                </button>
                                <a href="?event_id=<?php echo $event_id; ?>" class="btn btn-outline-secondary">
                                    <i class="bi bi-x-circle"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Tracking -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-people-fill"></i> Attendees
                                <?php if ($search): ?>
                                    <small class="text-muted">(<?php echo $filtered_attendees; ?> found, showing <?php echo count($attendees); ?>)</small>
                                <?php else: ?>
                                    <small class="text-muted">(<?php echo $total_attendees; ?> total, showing <?php echo count($attendees); ?>)</small>
                                <?php endif; ?>
                            </h5>
                        </div>
                        <div class="col-md-6 text-end">
                            <?php
                            $marked_count = array_filter($attendees, function($a) { return $a['actually_attended'] !== null; });
                            $attended_count = array_filter($attendees, function($a) { return $a['actually_attended'] == 1; });
                            ?>
                            <span class="badge bg-info me-1">
                                <?php echo count($marked_count); ?>/<?php echo count($attendees); ?> marked
                            </span>
                            <span class="badge bg-success">
                                <?php echo count($attended_count); ?> attended
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($attendees)): ?>
                        <div class="text-center text-muted py-5">
                            <i class="bi bi-person-x fs-1 d-block mb-3"></i>
                            <h5>No Attendees Found</h5>
                            <?php if ($search): ?>
                                <p>No attendees match your search criteria.</p>
                                <a href="?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                                    <i class="bi bi-arrow-left"></i> Show All Attendees
                                </a>
                            <?php else: ?>
                                <p>No one has RSVP'd as "attending" for this event yet.</p>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>

                        <!-- Bulk Operations Controls -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="card bg-light">
                                    <div class="card-body py-2">
                                        <div class="row align-items-center">
                                            <div class="col-md-6">
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAll()">
                                                        <i class="bi bi-check-square"></i> Select All
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNone()">
                                                        <i class="bi bi-square"></i> Select None
                                                    </button>
                                                </div>
                                                <span class="ms-3 text-muted">
                                                    <span id="selected-count">0</span> selected
                                                </span>
                                            </div>
                                            <div class="col-md-6 text-end">
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-success" onclick="bulkMarkSelected('attended')" id="bulk-attended-btn" disabled>
                                                        <i class="bi bi-check-circle"></i> Mark Selected Attended
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-danger" onclick="bulkMarkSelected('not_attended')" id="bulk-not-attended-btn" disabled>
                                                        <i class="bi bi-x-circle"></i> Mark Selected Not Attended
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Global Bulk Operations -->
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <strong><i class="bi bi-info-circle"></i> Global Bulk Operations</strong>
                                            <p class="mb-0 small">These operations affect ALL <?php echo $total_attendees; ?> attendees, not just those shown on this page.</p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-success" onclick="confirmBulkOperation('bulk_mark_attended')">
                                                    <i class="bi bi-check-all"></i> Mark All Attended
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="confirmBulkOperation('bulk_mark_not_attended')">
                                                    <i class="bi bi-x-circle-fill"></i> Mark All Not Attended
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="confirmBulkOperation('bulk_clear_attendance')">
                                                    <i class="bi bi-arrow-clockwise"></i> Clear All
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Smart Attendance Operations -->
                        <?php
                        // Check if this event has sessions
                        $sessions_stmt = $conn->prepare("SELECT COUNT(*) FROM event_sessions WHERE event_id = ?");
                        $sessions_stmt->execute([$event_id]);
                        $session_count = $sessions_stmt->fetchColumn();

                        if ($session_count > 0):
                        ?>
                        <div class="row mb-3">
                            <div class="col-md-12">
                                <div class="alert alert-warning">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <strong><i class="bi bi-lightbulb"></i> Smart Attendance</strong>
                                            <p class="mb-0 small">This event has <?php echo $session_count; ?> session(s). You can automatically mark event attendance based on session attendance.</p>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <div class="input-group input-group-sm">
                                                <span class="input-group-text">Min Sessions:</span>
                                                <select class="form-select" id="min-sessions-select">
                                                    <option value="1">1</option>
                                                    <?php for ($i = 2; $i <= min($session_count, 10); $i++): ?>
                                                        <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                                    <?php endfor; ?>
                                                </select>
                                                <button type="button" class="btn btn-warning" onclick="confirmSmartAttendance()">
                                                    <i class="bi bi-magic"></i> Smart Mark
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?>
                        <form method="POST" action="" id="attendance-form">
                            <input type="hidden" name="action" value="update_attendance">
                            <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead class="table-dark">
                                        <tr>
                                            <th width="40">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="select-all-checkbox" onchange="toggleSelectAll()">
                                                </div>
                                            </th>
                                            <th>Name</th>
                                            <th>Type</th>
                                            <th>Email</th>
                                            <th>Phone</th>
                                            <th>RSVP Date</th>
                                            <th>Notes</th>
                                            <th class="text-center">Actually Attended</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($attendees as $attendee): ?>
                                            <tr>
                                                <td>
                                                    <div class="form-check">
                                                        <input class="form-check-input attendee-checkbox"
                                                               type="checkbox"
                                                               name="selected_attendees[]"
                                                               value="<?php echo $attendee['rsvp_id']; ?>"
                                                               onchange="updateSelectedCount()">
                                                    </div>
                                                </td>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($attendee['full_name']); ?></strong>
                                                </td>
                                                <td>
                                                    <?php if ($attendee['attendee_type'] === 'guest'): ?>
                                                        <span class="badge bg-info">Guest</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-primary">Member</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <a href="mailto:<?php echo htmlspecialchars($attendee['email']); ?>">
                                                        <?php echo htmlspecialchars($attendee['email']); ?>
                                                    </a>
                                                </td>
                                                <td>
                                                    <?php if ($attendee['phone_number']): ?>
                                                        <a href="tel:<?php echo htmlspecialchars($attendee['phone_number']); ?>">
                                                            <?php echo htmlspecialchars($attendee['phone_number']); ?>
                                                        </a>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php 
                                                    $rsvp_date = new DateTime($attendee['rsvp_date']);
                                                    echo $rsvp_date->format('M j, Y');
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php if ($attendee['notes']): ?>
                                                        <span class="text-truncate" style="max-width: 150px;" 
                                                              title="<?php echo htmlspecialchars($attendee['notes']); ?>">
                                                            <?php echo htmlspecialchars($attendee['notes']); ?>
                                                        </span>
                                                    <?php else: ?>
                                                        <span class="text-muted">-</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="text-center">
                                                    <div class="form-check form-switch d-inline-block">
                                                        <input class="form-check-input" 
                                                               type="checkbox" 
                                                               name="attendance[<?php echo $attendee['rsvp_id']; ?>]" 
                                                               value="1"
                                                               id="attendance_<?php echo $attendee['rsvp_id']; ?>"
                                                               <?php echo ($attendee['actually_attended'] == 1) ? 'checked' : ''; ?>>
                                                        <label class="form-check-label" 
                                                               for="attendance_<?php echo $attendee['rsvp_id']; ?>">
                                                            <?php 
                                                            if ($attendee['actually_attended'] === null) {
                                                                echo '<span class="text-muted">Not marked</span>';
                                                            } elseif ($attendee['actually_attended'] == 1) {
                                                                echo '<span class="text-success">Attended</span>';
                                                            } else {
                                                                echo '<span class="text-danger">Did not attend</span>';
                                                            }
                                                            ?>
                                                        </label>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination Controls -->
                            <?php if ($total_pages > 1): ?>
                                <div class="d-flex justify-content-between align-items-center mt-4">
                                    <div>
                                        <span class="text-muted">
                                            Showing <?php echo (($page - 1) * $limit) + 1; ?> to <?php echo min($page * $limit, $filtered_attendees); ?>
                                            of <?php echo $filtered_attendees; ?> attendees
                                        </span>
                                    </div>
                                    <nav aria-label="Attendee pagination">
                                        <ul class="pagination pagination-sm mb-0">
                                            <?php if ($has_previous): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?event_id=<?php echo $event_id; ?>&page=<?php echo $page - 1; ?>&limit=<?php echo $limit; ?>&search=<?php echo urlencode($search); ?>">
                                                        <i class="bi bi-chevron-left"></i> Previous
                                                    </a>
                                                </li>
                                            <?php endif; ?>

                                            <?php
                                            $start_page = max(1, $page - 2);
                                            $end_page = min($total_pages, $page + 2);

                                            for ($i = $start_page; $i <= $end_page; $i++):
                                            ?>
                                                <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?event_id=<?php echo $event_id; ?>&page=<?php echo $i; ?>&limit=<?php echo $limit; ?>&search=<?php echo urlencode($search); ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php endfor; ?>

                                            <?php if ($has_next): ?>
                                                <li class="page-item">
                                                    <a class="page-link" href="?event_id=<?php echo $event_id; ?>&page=<?php echo $page + 1; ?>&limit=<?php echo $limit; ?>&search=<?php echo urlencode($search); ?>">
                                                        Next <i class="bi bi-chevron-right"></i>
                                                    </a>
                                                </li>
                                            <?php endif; ?>
                                        </ul>
                                    </nav>
                                </div>
                            <?php endif; ?>

                            <div class="d-flex justify-content-between align-items-center mt-4">
                                <div>
                                    <button type="button" class="btn btn-outline-success" onclick="checkAllOnPage()">
                                        <i class="bi bi-check-all"></i> Check All on Page
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="uncheckAllOnPage()">
                                        <i class="bi bi-x-circle"></i> Uncheck All on Page
                                    </button>
                                </div>
                                <div>
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-save"></i> Save Page Attendance
                                    </button>
                                </div>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Legacy functions for attendance checkboxes
function checkAllOnPage() {
    document.querySelectorAll('input[type="checkbox"][name^="attendance"]').forEach(function(checkbox) {
        checkbox.checked = true;
    });
}

function uncheckAllOnPage() {
    document.querySelectorAll('input[type="checkbox"][name^="attendance"]').forEach(function(checkbox) {
        checkbox.checked = false;
    });
}

// Cross-page selection persistence
const STORAGE_KEY = 'event_<?php echo $event_id; ?>_selected_attendees';

function getStoredSelections() {
    try {
        const stored = sessionStorage.getItem(STORAGE_KEY);
        return stored ? JSON.parse(stored) : [];
    } catch (e) {
        console.warn('Error reading stored selections:', e);
        return [];
    }
}

function saveStoredSelections(selectedIds) {
    try {
        sessionStorage.setItem(STORAGE_KEY, JSON.stringify(selectedIds));
    } catch (e) {
        console.warn('Error saving selections:', e);
    }
}

function clearStoredSelections() {
    try {
        sessionStorage.removeItem(STORAGE_KEY);
    } catch (e) {
        console.warn('Error clearing selections:', e);
    }
}

function getAllSelectedIds() {
    const storedSelections = getStoredSelections();
    const currentPageSelections = [];

    document.querySelectorAll('.attendee-checkbox:checked').forEach(function(checkbox) {
        currentPageSelections.push(checkbox.value);
    });

    // Merge stored selections with current page selections
    const allSelections = [...new Set([...storedSelections, ...currentPageSelections])];

    // Remove any unchecked items from current page
    document.querySelectorAll('.attendee-checkbox:not(:checked)').forEach(function(checkbox) {
        const index = allSelections.indexOf(checkbox.value);
        if (index > -1) {
            allSelections.splice(index, 1);
        }
    });

    return allSelections;
}

// Enhanced bulk selection functions
function selectAll() {
    document.querySelectorAll('.attendee-checkbox').forEach(function(checkbox) {
        checkbox.checked = true;
    });
    updateSelectedCount();
    document.getElementById('select-all-checkbox').checked = true;
}

function selectNone() {
    document.querySelectorAll('.attendee-checkbox').forEach(function(checkbox) {
        checkbox.checked = false;
    });
    updateSelectedCount();
    document.getElementById('select-all-checkbox').checked = false;
}

function selectAllPages() {
    // This would select all attendees across all pages
    if (confirm('This will select ALL <?php echo $total_attendees; ?> attendees across all pages. Continue?')) {
        // Store a special flag to indicate all pages are selected
        sessionStorage.setItem(STORAGE_KEY + '_all_selected', 'true');
        selectAll(); // Select current page
        updateSelectedCount();
    }
}

function selectNonePages() {
    // Clear all selections across all pages
    clearStoredSelections();
    sessionStorage.removeItem(STORAGE_KEY + '_all_selected');
    selectNone(); // Clear current page
    updateSelectedCount();
}

function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    const attendeeCheckboxes = document.querySelectorAll('.attendee-checkbox');

    attendeeCheckboxes.forEach(function(checkbox) {
        checkbox.checked = selectAllCheckbox.checked;
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    // Save current page selections to storage
    const allSelectedIds = getAllSelectedIds();
    saveStoredSelections(allSelectedIds);

    // Check if all pages are selected
    const allPagesSelected = sessionStorage.getItem(STORAGE_KEY + '_all_selected') === 'true';

    // Count selections
    const currentPageSelected = document.querySelectorAll('.attendee-checkbox:checked').length;
    const totalSelected = allPagesSelected ? <?php echo $total_attendees; ?> : allSelectedIds.length;

    // Update display
    const countDisplay = allPagesSelected ?
        `ALL ${<?php echo $total_attendees; ?>}` :
        `${totalSelected}`;

    document.getElementById('selected-count').textContent = countDisplay;

    // Enable/disable bulk action buttons
    const bulkButtons = ['bulk-attended-btn', 'bulk-not-attended-btn'];
    bulkButtons.forEach(function(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = totalSelected === 0;
        }
    });

    // Update select all checkbox state for current page
    const totalCheckboxes = document.querySelectorAll('.attendee-checkbox').length;
    const selectAllCheckbox = document.getElementById('select-all-checkbox');
    if (currentPageSelected === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (currentPageSelected === totalCheckboxes) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }

    // Update cross-page selection info
    updateCrossPageInfo(totalSelected, allPagesSelected);
}

function updateCrossPageInfo(totalSelected, allPagesSelected) {
    let crossPageInfo = document.getElementById('cross-page-info');
    if (!crossPageInfo) {
        // Create cross-page info element
        crossPageInfo = document.createElement('div');
        crossPageInfo.id = 'cross-page-info';
        crossPageInfo.className = 'mt-2 small text-muted';
        document.getElementById('selected-count').parentNode.appendChild(crossPageInfo);
    }

    if (allPagesSelected) {
        crossPageInfo.innerHTML = '<i class="bi bi-info-circle"></i> All attendees across all pages selected';
        crossPageInfo.className = 'mt-2 small text-warning';
    } else if (totalSelected > document.querySelectorAll('.attendee-checkbox:checked').length) {
        const crossPageCount = totalSelected - document.querySelectorAll('.attendee-checkbox:checked').length;
        crossPageInfo.innerHTML = `<i class="bi bi-info-circle"></i> +${crossPageCount} selected on other pages`;
        crossPageInfo.className = 'mt-2 small text-info';
    } else {
        crossPageInfo.innerHTML = '';
        crossPageInfo.className = 'mt-2 small text-muted';
    }
}

function bulkMarkSelected(action) {
    // Check if all pages are selected
    const allPagesSelected = sessionStorage.getItem(STORAGE_KEY + '_all_selected') === 'true';

    if (allPagesSelected) {
        // Use global bulk operation for all pages
        const actionText = action === 'attended' ? 'attended' : 'not attended';
        if (!confirm(`Are you sure you want to mark ALL ${<?php echo $total_attendees; ?>} attendees as ${actionText}?`)) {
            return;
        }

        const globalAction = action === 'attended' ? 'bulk_mark_attended' : 'bulk_mark_not_attended';
        confirmBulkOperation(globalAction);
        return;
    }

    // Get all selected IDs across pages
    const allSelectedIds = getAllSelectedIds();

    if (allSelectedIds.length === 0) {
        alert('Please select at least one attendee.');
        return;
    }

    const actionText = action === 'attended' ? 'attended' : 'not attended';
    const countText = allSelectedIds.length === 1 ? '1 attendee' : `${allSelectedIds.length} attendees`;

    if (!confirm(`Are you sure you want to mark ${countText} as ${actionText}?`)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '';

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action === 'attended' ? 'bulk_selected_attended' : 'bulk_selected_not_attended';
    form.appendChild(actionInput);

    // Add all selected IDs (from all pages)
    allSelectedIds.forEach(function(id) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'selected_attendees[]';
        input.value = id;
        form.appendChild(input);
    });

    // Add flag to preserve selections after operation
    const preserveInput = document.createElement('input');
    preserveInput.type = 'hidden';
    preserveInput.name = 'preserve_selections';
    preserveInput.value = '1';
    form.appendChild(preserveInput);

    document.body.appendChild(form);
    form.submit();
}

function confirmBulkOperation(action) {
    let message = '';
    let totalCount = <?php echo $total_attendees; ?>;

    switch(action) {
        case 'bulk_mark_attended':
            message = `Are you sure you want to mark ALL ${totalCount} attendees as attended? This cannot be undone.`;
            break;
        case 'bulk_mark_not_attended':
            message = `Are you sure you want to mark ALL ${totalCount} attendees as not attended? This cannot be undone.`;
            break;
        case 'bulk_clear_attendance':
            message = `Are you sure you want to clear attendance marks for ALL ${totalCount} attendees? This cannot be undone.`;
            break;
    }

    if (!confirm(message)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '';

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;
    form.appendChild(actionInput);

    document.body.appendChild(form);
    form.submit();
}

function confirmSmartAttendance() {
    const minSessions = document.getElementById('min-sessions-select').value;
    const sessionCount = <?php echo isset($session_count) ? $session_count : 0; ?>;

    if (sessionCount === 0) {
        alert('This event has no sessions. Smart attendance marking is not available.');
        return;
    }

    const message = `This will automatically mark event attendees as "attended" if they attended at least ${minSessions} session(s). This action cannot be undone. Continue?`;

    if (!confirm(message)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '';

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'smart_mark_from_sessions';
    form.appendChild(actionInput);

    const minSessionsInput = document.createElement('input');
    minSessionsInput.type = 'hidden';
    minSessionsInput.name = 'min_sessions';
    minSessionsInput.value = minSessions;
    form.appendChild(minSessionsInput);

    document.body.appendChild(form);
    form.submit();
}

function restoreSelections() {
    // Check if selections are preserved from URL (after bulk operation)
    const urlParams = new URLSearchParams(window.location.search);
    const preservedSelections = urlParams.get('preserve_selections');

    if (preservedSelections) {
        try {
            const preservedIds = JSON.parse(decodeURIComponent(preservedSelections));
            saveStoredSelections(preservedIds);

            // Clean up URL
            urlParams.delete('preserve_selections');
            const newUrl = window.location.pathname + '?' + urlParams.toString();
            window.history.replaceState({}, '', newUrl);
        } catch (e) {
            console.warn('Error parsing preserved selections:', e);
        }
    }

    const storedSelections = getStoredSelections();
    const allPagesSelected = sessionStorage.getItem(STORAGE_KEY + '_all_selected') === 'true';

    if (allPagesSelected) {
        // If all pages are selected, select all on current page
        document.querySelectorAll('.attendee-checkbox').forEach(function(checkbox) {
            checkbox.checked = true;
        });
    } else {
        // Restore individual selections for current page
        document.querySelectorAll('.attendee-checkbox').forEach(function(checkbox) {
            if (storedSelections.includes(checkbox.value)) {
                checkbox.checked = true;
            }
        });
    }

    updateSelectedCount();
}

function addCrossPageControls() {
    // Add cross-page selection controls
    const selectAllBtn = document.querySelector('button[onclick="selectAll()"]');
    const selectNoneBtn = document.querySelector('button[onclick="selectNone()"]');

    if (selectAllBtn && selectNoneBtn) {
        const parentGroup = selectAllBtn.parentNode;

        // Add "Select All Pages" button
        const selectAllPagesBtn = document.createElement('button');
        selectAllPagesBtn.type = 'button';
        selectAllPagesBtn.className = 'btn btn-sm btn-warning';
        selectAllPagesBtn.onclick = selectAllPages;
        selectAllPagesBtn.innerHTML = '<i class="bi bi-check-square-fill"></i> Select All Pages';
        selectAllPagesBtn.title = 'Select all <?php echo $total_attendees; ?> attendees across all pages';

        // Add "Clear All Pages" button
        const clearAllPagesBtn = document.createElement('button');
        clearAllPagesBtn.type = 'button';
        clearAllPagesBtn.className = 'btn btn-sm btn-outline-warning';
        clearAllPagesBtn.onclick = selectNonePages;
        clearAllPagesBtn.innerHTML = '<i class="bi bi-square"></i> Clear All Pages';
        clearAllPagesBtn.title = 'Clear all selections across all pages';

        // Insert after existing buttons
        parentGroup.appendChild(selectAllPagesBtn);
        parentGroup.appendChild(clearAllPagesBtn);
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    restoreSelections();
    addCrossPageControls();
    updateSelectedCount();
});
</script>

<!-- CSV Import Modal -->
<div class="modal fade" id="importCsvModal" tabindex="-1" aria-labelledby="importCsvModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importCsvModalLabel">
                    <i class="bi bi-upload"></i> Import Attendance from CSV
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" name="action" value="import_csv">
                    <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">

                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> CSV Import Instructions</h6>
                        <ul class="mb-0">
                            <li>CSV must have headers: <code>RSVP ID, Actually Attended</code></li>
                            <li>RSVP ID should match the exported format (numbers for members, guest_X for guests)</li>
                            <li>Actually Attended should be: <code>1</code> (attended), <code>0</code> (not attended), or empty (not marked)</li>
                            <li>You can export current data, modify it, and re-import</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="csv_file" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                    </div>

                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="update_existing" name="update_existing" value="1" checked>
                        <label class="form-check-label" for="update_existing">
                            Update existing attendance records (recommended)
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-upload"></i> Import CSV
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Hidden forms for bulk operations -->
<div style="display: none;">
    <form id="bulk-form" method="POST" action="">
        <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">
        <input type="hidden" name="action" id="bulk-action">
    </form>
</div>

<?php include 'includes/footer.php'; ?>
