<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

// Include notification manager
require_once '../classes/SessionNotificationManager.php';

// Database connection
$conn = $pdo;

// Initialize notification manager
$sessionNotificationManager = null;
try {
    $sessionNotificationManager = new SessionNotificationManager($pdo);
} catch (Exception $e) {
    error_log("Failed to initialize SessionNotificationManager: " . $e->getMessage());
}

$message = '';
$error = '';

// Get session ID from URL
$session_id = isset($_GET['session_id']) ? (int)$_GET['session_id'] : 0;

if ($session_id <= 0) {
    header("Location: events.php");
    exit();
}

// Get session and event details
try {
    $stmt = $conn->prepare("
        SELECT es.*, e.title as event_title, e.id as event_id
        FROM event_sessions es
        JOIN events e ON es.event_id = e.id
        WHERE es.id = ?
    ");
    $stmt->execute([$session_id]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading session: " . $e->getMessage();
    $session = null;
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] === 'add_registration') {
        $member_id = !empty($_POST['member_id']) ? (int)$_POST['member_id'] : null;
        $guest_name = trim($_POST['guest_name']);
        $guest_email = trim($_POST['guest_email']);

        if (!$member_id && (empty($guest_name) || empty($guest_email))) {
            $error = "Please select a member or provide guest details.";
        } else {
            // Check if the session is in the past
            try {
                $stmt = $conn->prepare("
                    SELECT start_datetime, session_title
                    FROM event_sessions
                    WHERE id = ?
                ");
                $stmt->execute([$session_id]);
                $session_details = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$session_details) {
                    $error = "Session not found.";
                } elseif (strtotime($session_details['start_datetime']) < time()) {
                    $error = "Cannot register for past sessions. The session '" . htmlspecialchars($session_details['session_title']) . "' has already started or ended.";
                } else {
                    // Proceed with registration
                    $stmt = $conn->prepare("
                        INSERT INTO session_attendance (session_id, member_id, guest_name, guest_email, attendance_status)
                        VALUES (?, ?, ?, ?, 'registered')
                    ");
                    $stmt->execute([$session_id, $member_id, $guest_name ?: null, $guest_email ?: null]);

                    // Send notifications
                    if ($sessionNotificationManager) {
                        try {
                            // Send registration confirmation to the registrant
                            if ($member_id) {
                                $sessionNotificationManager->sendRegistrationConfirmation($session_id, $member_id);
                            } else {
                                $sessionNotificationManager->sendRegistrationConfirmation($session_id, null, $guest_email, $guest_name);
                            }

                            // Notify admins about the new registration
                            $sessionNotificationManager->notifyAdminNewRegistration($session_id, $member_id, $guest_name, $guest_email);
                        } catch (Exception $e) {
                            error_log("Failed to send session registration notifications: " . $e->getMessage());
                            // Don't fail the registration if notifications fail
                        }
                    }

                    $message = "Registration added successfully!";

                    // Clear form data by redirecting
                    header("Location: session_attendance.php?session_id=" . $session_id . "&added=1");
                    exit();
                }

            } catch (PDOException $e) {
                if ($e->getCode() == 23000) { // Duplicate entry
                    $error = "This person is already registered for this session.";
                } else {
                    $error = "Error adding registration: " . $e->getMessage();
                }
            }
        }
    } elseif ($_POST['action'] === 'update_attendance') {
        $attendance_id = (int)$_POST['attendance_id'];
        $attendance_status = $_POST['attendance_status'];
        $notes = trim($_POST['notes']);

        try {
            $stmt = $conn->prepare("
                UPDATE session_attendance SET
                    attendance_status = ?, notes = ?,
                    attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                WHERE id = ? AND session_id = ?
            ");
            $stmt->execute([$attendance_status, $notes, $attendance_status, $attendance_id, $session_id]);
            
            $message = "Attendance updated successfully!";
            
            // Clear form data by redirecting
            header("Location: session_attendance.php?session_id=" . $session_id . "&updated=1");
            exit();
            
        } catch (PDOException $e) {
            $error = "Error updating attendance: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'delete_registration') {
        $attendance_id = (int)$_POST['attendance_id'];
        
        try {
            $stmt = $conn->prepare("DELETE FROM session_attendance WHERE id = ? AND session_id = ?");
            $stmt->execute([$attendance_id, $session_id]);
            
            $message = "Registration removed successfully!";
            
            // Clear form data by redirecting
            header("Location: session_attendance.php?session_id=" . $session_id . "&deleted=1");
            exit();
            
        } catch (PDOException $e) {
            $error = "Error removing registration: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'bulk_mark_attended') {
        // Mark all session registrants as attended
        try {
            $stmt = $conn->prepare("
                UPDATE session_attendance
                SET attendance_status = 'attended', attendance_date = NOW()
                WHERE session_id = ? AND attendance_status = 'registered'
            ");
            $stmt->execute([$session_id]);
            $affected_rows = $stmt->rowCount();

            $message = "Successfully marked {$affected_rows} registrants as attended.";

            header("Location: session_attendance.php?session_id=" . $session_id . "&bulk_updated=1");
            exit();

        } catch (PDOException $e) {
            $error = "Error marking attendees as attended: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'bulk_mark_no_show') {
        // Mark all session registrants as no-show
        try {
            $stmt = $conn->prepare("
                UPDATE session_attendance
                SET attendance_status = 'no_show'
                WHERE session_id = ? AND attendance_status = 'registered'
            ");
            $stmt->execute([$session_id]);
            $affected_rows = $stmt->rowCount();

            $message = "Successfully marked {$affected_rows} registrants as no-show.";

            header("Location: session_attendance.php?session_id=" . $session_id . "&bulk_updated=1");
            exit();

        } catch (PDOException $e) {
            $error = "Error marking attendees as no-show: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'bulk_reset_attendance') {
        // Reset all session attendance to registered
        try {
            $stmt = $conn->prepare("
                UPDATE session_attendance
                SET attendance_status = 'registered', attendance_date = NULL
                WHERE session_id = ?
            ");
            $stmt->execute([$session_id]);
            $affected_rows = $stmt->rowCount();

            $message = "Successfully reset attendance for {$affected_rows} registrants.";

            header("Location: session_attendance.php?session_id=" . $session_id . "&bulk_updated=1");
            exit();

        } catch (PDOException $e) {
            $error = "Error resetting attendance: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'bulk_selected_attended') {
        // Mark selected attendees as attended
        $selected_ids = $_POST['selected_attendees'] ?? [];
        if (!empty($selected_ids)) {
            try {
                $placeholders = str_repeat('?,', count($selected_ids) - 1) . '?';
                $stmt = $conn->prepare("
                    UPDATE session_attendance
                    SET attendance_status = 'attended', attendance_date = NOW()
                    WHERE id IN ($placeholders) AND session_id = ?
                ");
                $params = array_merge($selected_ids, [$session_id]);
                $stmt->execute($params);
                $affected_rows = $stmt->rowCount();

                $message = "Successfully marked {$affected_rows} selected attendees as attended.";

                // If preserve_selections is set, redirect with selections preserved
                if (isset($_POST['preserve_selections'])) {
                    $selected_ids = $_POST['selected_attendees'] ?? [];
                    $redirect_url = "session_attendance.php?session_id=" . $session_id . "&bulk_updated=1&preserve_selections=" . urlencode(json_encode($selected_ids));
                    header("Location: " . $redirect_url);
                    exit();
                }

                header("Location: session_attendance.php?session_id=" . $session_id . "&bulk_updated=1");
                exit();

            } catch (PDOException $e) {
                $error = "Error marking selected attendees as attended: " . $e->getMessage();
            }
        } else {
            $error = "No attendees selected.";
        }
    } elseif ($_POST['action'] === 'bulk_selected_no_show') {
        // Mark selected attendees as no-show
        $selected_ids = $_POST['selected_attendees'] ?? [];
        if (!empty($selected_ids)) {
            try {
                $placeholders = str_repeat('?,', count($selected_ids) - 1) . '?';
                $stmt = $conn->prepare("
                    UPDATE session_attendance
                    SET attendance_status = 'no_show'
                    WHERE id IN ($placeholders) AND session_id = ?
                ");
                $params = array_merge($selected_ids, [$session_id]);
                $stmt->execute($params);
                $affected_rows = $stmt->rowCount();

                $message = "Successfully marked {$affected_rows} selected attendees as no-show.";

                // If preserve_selections is set, redirect with selections preserved
                if (isset($_POST['preserve_selections'])) {
                    $selected_ids = $_POST['selected_attendees'] ?? [];
                    $redirect_url = "session_attendance.php?session_id=" . $session_id . "&bulk_updated=1&preserve_selections=" . urlencode(json_encode($selected_ids));
                    header("Location: " . $redirect_url);
                    exit();
                }

                header("Location: session_attendance.php?session_id=" . $session_id . "&bulk_updated=1");
                exit();

            } catch (PDOException $e) {
                $error = "Error marking selected attendees as no-show: " . $e->getMessage();
            }
        } else {
            $error = "No attendees selected.";
        }
    } elseif ($_POST['action'] === 'quick_mark_individual') {
        // Quick individual attendance marking
        $attendance_id = $_POST['attendance_id'] ?? '';
        $attendance_status = $_POST['attendance_status'] ?? '';

        if (!empty($attendance_id) && in_array($attendance_status, ['registered', 'attended', 'no_show'])) {
            try {
                $stmt = $conn->prepare("
                    UPDATE session_attendance
                    SET attendance_status = ?, attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                    WHERE id = ? AND session_id = ?
                ");
                $stmt->execute([$attendance_status, $attendance_status, $attendance_id, $session_id]);

                if ($stmt->rowCount() > 0) {
                    $status_text = ucfirst(str_replace('_', ' ', $attendance_status));
                    $message = "Attendee marked as {$status_text} successfully!";

                    // Redirect to prevent form resubmission
                    $redirect_url = "session_attendance.php?session_id=" . $session_id . "&individual_updated=1";
                    header("Location: " . $redirect_url);
                    exit();
                } else {
                    $error = "Failed to update attendance. Attendee not found or no changes made.";
                }

            } catch (PDOException $e) {
                $error = "Error updating individual attendance: " . $e->getMessage();
            }
        } else {
            $error = "Invalid attendance data provided.";
        }
    }
}

// Check for success messages from redirect
if (isset($_GET['added']) && $_GET['added'] == '1') {
    $message = "Registration added successfully!";
} elseif (isset($_GET['updated']) && $_GET['updated'] == '1') {
    $message = "Attendance updated successfully!";
} elseif (isset($_GET['deleted']) && $_GET['deleted'] == '1') {
    $message = "Registration removed successfully!";
} elseif (isset($_GET['bulk_updated']) && $_GET['bulk_updated'] == '1') {
    $message = "Bulk attendance operation completed successfully!";
}

// Include pagination component
require_once 'includes/pagination.php';

// Get search parameters
$search = isset($_GET['search']) ? trim($_GET['search']) : '';
$page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
$limit = isset($_GET['limit']) ? max(10, min(100, intval($_GET['limit']))) : 20;

// Build search conditions
$where_conditions = ["sa.session_id = ?"];
$params = [$session_id];

if (!empty($search)) {
    $where_conditions[] = "(m.full_name LIKE ? OR m.email LIKE ? OR m.phone LIKE ? OR sa.guest_name LIKE ? OR sa.guest_email LIKE ?)";
    $search_term = '%' . $search . '%';
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
    $params[] = $search_term;
}

$where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

// Get total count for pagination
$count_sql = "SELECT COUNT(*) FROM session_attendance sa LEFT JOIN members m ON sa.member_id = m.id $where_clause";
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($params);
$total_records = $count_stmt->fetchColumn();

// Calculate pagination
$pagination = calculate_pagination($total_records, $page, $limit);
$offset = $pagination['offset'];

// Get attendance records for this session with search and pagination
$attendance_records = [];
try {
    $stmt = $conn->prepare("
        SELECT sa.*,
               COALESCE(m.full_name, sa.guest_name, 'Unknown') as member_name,
               COALESCE(m.email, sa.guest_email) as member_email,
               COALESCE(m.phone_number, '') as member_phone
        FROM session_attendance sa
        LEFT JOIN members m ON sa.member_id = m.id
        $where_clause
        ORDER BY sa.registration_date ASC
        LIMIT $limit OFFSET $offset
    ");
    $stmt->execute($params);
    $attendance_records = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Debug logging
    error_log("SESSION ATTENDANCE DEBUG - Session ID: $session_id");
    error_log("SESSION ATTENDANCE DEBUG - Total records from count: $total_records");
    error_log("SESSION ATTENDANCE DEBUG - Records fetched: " . count($attendance_records));
    error_log("SESSION ATTENDANCE DEBUG - SQL: " . $stmt->queryString);
    error_log("SESSION ATTENDANCE DEBUG - Params: " . print_r($params, true));
    if (!empty($attendance_records)) {
        error_log("SESSION ATTENDANCE DEBUG - First record: " . print_r($attendance_records[0], true));
    }
} catch (PDOException $e) {
    error_log("Error loading attendance records: " . $e->getMessage());
}

// Get members for dropdown
$members = [];
try {
    $stmt = $conn->prepare("SELECT id, full_name, email FROM members WHERE status = 'active' ORDER BY full_name ASC");
    $stmt->execute();
    $members = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error loading members: " . $e->getMessage());
}

// Calculate statistics (use total counts, not filtered results)
try {
    $stats_stmt = $conn->prepare("
        SELECT
            COUNT(*) as total_registered,
            COUNT(CASE WHEN attendance_status = 'attended' THEN 1 END) as total_attended,
            COUNT(CASE WHEN attendance_status = 'no_show' THEN 1 END) as total_no_show
        FROM session_attendance
        WHERE session_id = ?
    ");
    $stats_stmt->execute([$session_id]);
    $stats = $stats_stmt->fetch(PDO::FETCH_ASSOC);

    $total_registered = $stats['total_registered'];
    $total_attended = $stats['total_attended'];
    $total_no_show = $stats['total_no_show'];

    // Debug: Check what's actually in session_attendance table
    $debug_stmt = $conn->prepare("SELECT * FROM session_attendance WHERE session_id = ?");
    $debug_stmt->execute([$session_id]);
    $debug_records = $debug_stmt->fetchAll(PDO::FETCH_ASSOC);
    error_log("SESSION ATTENDANCE DEBUG - Raw session_attendance records: " . print_r($debug_records, true));

} catch (PDOException $e) {
    error_log("Error calculating statistics: " . $e->getMessage());
    $total_registered = 0;
    $total_attended = 0;
    $total_no_show = 0;
}

// Page title and header info
$page_title = 'Session Attendance - ' . ($session ? htmlspecialchars($session['session_title']) : 'Unknown Session');
$page_header = 'Session Attendance';
$page_description = 'Manage attendance for: ' . ($session ? htmlspecialchars($session['session_title']) : 'Unknown Session');

// Include header
include 'includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
    <div></div>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="event_sessions.php?event_id=<?php echo $session['event_id']; ?>" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Sessions
        </a>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($_GET['individual_updated'])): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> Individual attendance updated successfully!
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($session): ?>
<!-- Session Information -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="bi bi-info-circle"></i> Session Information
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4><?php echo htmlspecialchars($session['session_title']); ?></h4>
                <p class="text-muted mb-1"><strong>Event:</strong> <?php echo htmlspecialchars($session['event_title']); ?></p>
                <?php if (!empty($session['session_description'])): ?>
                    <p class="text-muted"><?php echo htmlspecialchars($session['session_description']); ?></p>
                <?php endif; ?>
            </div>
            <div class="col-md-4">
                <p><strong>Date:</strong> <?php echo date('M j, Y', strtotime($session['start_datetime'])); ?></p>
                <p><strong>Time:</strong> <?php echo date('g:i A', strtotime($session['start_datetime'])); ?> - <?php echo date('g:i A', strtotime($session['end_datetime'])); ?></p>
                <p><strong>Location:</strong> <?php echo htmlspecialchars($session['location'] ?: 'Not specified'); ?></p>
                <p><strong>Instructor:</strong> <?php echo htmlspecialchars($session['instructor_name'] ?: 'Not specified'); ?></p>
                <p><strong>Capacity:</strong> <?php echo $session['max_attendees'] ? number_format($session['max_attendees']) : 'Unlimited'; ?></p>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Statistics -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-primary"><?php echo $total_registered; ?></h3>
                <p class="card-text">Total Registered</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-success"><?php echo $total_attended; ?></h3>
                <p class="card-text">Attended</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-warning"><?php echo $total_no_show; ?></h3>
                <p class="card-text">No Show</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h3 class="text-info"><?php echo $total_registered > 0 ? round(($total_attended / $total_registered) * 100) : 0; ?>%</h3>
                <p class="card-text">Attendance Rate</p>
            </div>
        </div>
    </div>
</div>

<!-- Search Section -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" class="row g-3">
            <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">
            <div class="col-md-8">
                <label for="search" class="form-label">Search Attendees</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="<?= htmlspecialchars($search) ?>"
                       placeholder="Search by name, email, or phone...">
            </div>
            <div class="col-md-2">
                <label for="limit" class="form-label">Per Page</label>
                <select class="form-select" id="limit" name="limit">
                    <option value="10" <?= $limit == 10 ? 'selected' : '' ?>>10</option>
                    <option value="20" <?= $limit == 20 ? 'selected' : '' ?>>20</option>
                    <option value="50" <?= $limit == 50 ? 'selected' : '' ?>>50</option>
                    <option value="100" <?= $limit == 100 ? 'selected' : '' ?>>100</option>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-outline-primary w-100">
                    <i class="bi bi-search"></i> Search
                </button>
            </div>
        </form>
        <?php if (!empty($search)): ?>
            <div class="mt-2">
                <small class="text-muted">
                    Showing <?= count($attendance_records) ?> of <?= $total_records ?> results for "<?= htmlspecialchars($search) ?>"
                    <a href="session_attendance.php?session_id=<?= $session_id ?>" class="text-decoration-none ms-2">
                        <i class="bi bi-x-circle"></i> Clear search
                    </a>
                </small>
            </div>
        <?php endif; ?>
    </div>
</div>

<!-- Global Operations Information (Always Visible) -->
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="bi bi-info-circle"></i> Global Operations Information
        </h6>
    </div>
    <div class="card-body">
        <div class="alert alert-info mb-0">
            <strong><i class="bi bi-info-circle"></i> Global Operations</strong>
            <p class="mb-0 small">These operations affect ALL <?php echo $total_registered; ?> registered attendees, not just those shown on this page.</p>
        </div>

        <div class="row mt-3">
            <div class="col-md-12 text-center">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-success" onclick="confirmBulkSessionOperation('bulk_mark_attended')">
                        <i class="bi bi-check-all"></i> Mark All Attended
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="confirmBulkSessionOperation('bulk_mark_no_show')">
                        <i class="bi bi-x-circle-fill"></i> Mark All No-Show
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="confirmBulkSessionOperation('bulk_reset_attendance')">
                        <i class="bi bi-arrow-clockwise"></i> Reset All
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Operations (Shown when attendees exist) -->
<?php if (!empty($attendance_records)): ?>
<div class="card mb-3">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="bi bi-lightning"></i> Selection-Based Bulk Operations
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllAttendees()">
                        <i class="bi bi-check-square"></i> Select All
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNoneAttendees()">
                        <i class="bi bi-square"></i> Select None
                    </button>
                </div>
                <span class="ms-3 text-muted">
                    <span id="selected-attendees-count">0</span> selected
                </span>
            </div>
            <div class="col-md-6 text-end">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-sm btn-success" onclick="bulkMarkSelectedAttended()" id="bulk-selected-attended-btn" disabled>
                        <i class="bi bi-check-circle"></i> Mark Selected Attended
                    </button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="bulkMarkSelectedNoShow()" id="bulk-selected-no-show-btn" disabled>
                        <i class="bi bi-x-circle"></i> Mark Selected No-Show
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Attendance Management -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">
            <i class="bi bi-people"></i> Attendance Records
            <?php if (!empty($search)): ?>
                (<?= count($attendance_records) ?> of <?= $total_records ?> found)
            <?php else: ?>
                (<?= $total_records ?> total)
            <?php endif; ?>
        </h5>
        <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addRegistrationModal">
            <i class="bi bi-plus-circle"></i> Add Registration
        </button>
    </div>
    <div class="card-body">
        <?php if (empty($attendance_records)): ?>
            <div class="text-center py-5">
                <i class="bi bi-people display-1 text-muted"></i>
                <h4 class="mt-3">No Registrations Yet</h4>
                <p class="text-muted">This session doesn't have any registrations yet.</p>
                <p class="text-muted">Click "Add Registration" to register the first attendee.</p>
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th width="40">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="select-all-session-checkbox" onchange="toggleSelectAllSession()">
                                </div>
                            </th>
                            <th>Attendee</th>
                            <th>Email</th>
                            <th>Phone</th>
                            <th>Registration Date</th>
                            <th>Attendance Status</th>
                            <th>Attendance Date</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($attendance_records as $record): ?>
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input class="form-check-input session-attendee-checkbox"
                                               type="checkbox"
                                               name="selected_session_attendees[]"
                                               value="<?php echo $record['id']; ?>"
                                               onchange="updateSelectedSessionCount()">
                                    </div>
                                </td>
                                <td>
                                    <?php if ($record['member_id']): ?>
                                        <i class="bi bi-person-badge text-primary"></i> <?php echo htmlspecialchars($record['member_name'] ?: 'Unknown Member'); ?>
                                        <small class="text-muted d-block">Member</small>
                                    <?php else: ?>
                                        <i class="bi bi-person text-secondary"></i> <?php echo htmlspecialchars($record['guest_name'] ?: 'Unknown Guest'); ?>
                                        <small class="text-muted d-block">Guest</small>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($record['member_email'] ?: $record['guest_email'] ?: 'No email provided'); ?>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($record['member_phone'] ?: 'No phone provided'); ?>
                                </td>
                                <td>
                                    <?php echo date('M j, Y g:i A', strtotime($record['registration_date'])); ?>
                                </td>
                                <td>
                                    <?php
                                    $status_class = '';
                                    $status_icon = '';
                                    switch ($record['attendance_status']) {
                                        case 'registered':
                                            $status_class = 'bg-info';
                                            $status_icon = 'bi-clock';
                                            break;
                                        case 'attended':
                                            $status_class = 'bg-success';
                                            $status_icon = 'bi-check-circle';
                                            break;
                                        case 'no_show':
                                            $status_class = 'bg-warning';
                                            $status_icon = 'bi-x-circle';
                                            break;
                                    }
                                    ?>
                                    <span class="badge <?php echo $status_class; ?>">
                                        <i class="bi <?php echo $status_icon; ?>"></i> <?php echo ucfirst(str_replace('_', ' ', $record['attendance_status'])); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php echo $record['attendance_date'] ? date('M j, Y g:i A', strtotime($record['attendance_date'])) : '-'; ?>
                                </td>
                                <td>
                                    <?php echo htmlspecialchars($record['notes'] ?: '-'); ?>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <?php if ($record['attendance_status'] !== 'attended'): ?>
                                            <button type="button" class="btn btn-success" onclick="quickMarkAttendance(<?php echo $record['id']; ?>, 'attended')" title="Mark as Attended">
                                                <i class="bi bi-check-circle"></i>
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($record['attendance_status'] !== 'no_show'): ?>
                                            <button type="button" class="btn btn-warning" onclick="quickMarkAttendance(<?php echo $record['id']; ?>, 'no_show')" title="Mark as No-Show">
                                                <i class="bi bi-x-circle"></i>
                                            </button>
                                        <?php endif; ?>

                                        <?php if ($record['attendance_status'] !== 'registered'): ?>
                                            <button type="button" class="btn btn-outline-secondary" onclick="quickMarkAttendance(<?php echo $record['id']; ?>, 'registered')" title="Reset to Registered">
                                                <i class="bi bi-arrow-clockwise"></i>
                                            </button>
                                        <?php endif; ?>

                                        <button type="button" class="btn btn-outline-primary" onclick="editAttendance(<?php echo $record['id']; ?>)" title="Edit Details">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="btn btn-outline-danger" onclick="deleteRegistration(<?php echo $record['id']; ?>, '<?php echo htmlspecialchars($record['member_name'] ?: $record['guest_name']); ?>')" title="Remove Registration">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>

    <!-- Pagination -->
    <?php if ($pagination['total_pages'] > 1): ?>
        <div class="card-footer">
            <?php
            // Prepare URL parameters to preserve
            $url_params = ['session_id' => $session_id];
            if (!empty($search)) {
                $url_params['search'] = $search;
            }
            if ($limit != 20) {
                $url_params['limit'] = $limit;
            }

            echo generate_pagination(
                $pagination['current_page'],
                $pagination['total_pages'],
                $pagination['total_records'],
                $pagination['records_per_page'],
                'session_attendance.php',
                $url_params,
                'page'
            );
            ?>
        </div>
    <?php endif; ?>
</div>

<?php else: ?>
<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle"></i> Session not found or could not be loaded.
</div>
<?php endif; ?>

<!-- Add Registration Modal -->
<div class="modal fade" id="addRegistrationModal" tabindex="-1" aria-labelledby="addRegistrationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addRegistrationModalLabel">
                    <i class="bi bi-plus-circle"></i> Add Registration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addRegistrationForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="add_registration">
                    <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">

                    <div class="mb-3">
                        <label class="form-label">Registration Type</label>
                        <div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="registration_type" id="member_registration" value="member" checked onchange="toggleRegistrationType()">
                                <label class="form-check-label" for="member_registration">
                                    Existing Member
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="registration_type" id="guest_registration" value="guest" onchange="toggleRegistrationType()">
                                <label class="form-check-label" for="guest_registration">
                                    Guest
                                </label>
                            </div>
                        </div>
                    </div>

                    <div id="member_fields">
                        <div class="mb-3">
                            <label for="member_id" class="form-label">Select Member</label>
                            <select class="form-select" id="member_id" name="member_id">
                                <option value="">Choose a member...</option>
                                <?php foreach ($members as $member): ?>
                                    <option value="<?php echo $member['id']; ?>"><?php echo htmlspecialchars($member['full_name']); ?> (<?php echo htmlspecialchars($member['email']); ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div id="guest_fields" style="display: none;">
                        <div class="mb-3">
                            <label for="guest_name" class="form-label">Guest Name</label>
                            <input type="text" class="form-control" id="guest_name" name="guest_name">
                        </div>
                        <div class="mb-3">
                            <label for="guest_email" class="form-label">Guest Email</label>
                            <input type="email" class="form-control" id="guest_email" name="guest_email">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> Add Registration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Attendance Modal -->
<div class="modal fade" id="editAttendanceModal" tabindex="-1" aria-labelledby="editAttendanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editAttendanceModalLabel">
                    <i class="bi bi-pencil"></i> Update Attendance
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editAttendanceForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="update_attendance">
                    <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">
                    <input type="hidden" name="attendance_id" id="edit_attendance_id">

                    <div class="mb-3">
                        <label class="form-label">Attendee</label>
                        <p id="edit_attendee_name" class="form-control-plaintext"></p>
                    </div>

                    <div class="mb-3">
                        <label for="edit_attendance_status" class="form-label">Attendance Status</label>
                        <select class="form-select" id="edit_attendance_status" name="attendance_status" required>
                            <option value="registered">Registered</option>
                            <option value="attended">Attended</option>
                            <option value="no_show">No Show</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="edit_notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="edit_notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Attendance
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Registration Modal -->
<div class="modal fade" id="deleteRegistrationModal" tabindex="-1" aria-labelledby="deleteRegistrationModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteRegistrationModalLabel">
                    <i class="bi bi-exclamation-triangle text-danger"></i> Remove Registration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="deleteRegistrationForm" method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="delete_registration">
                    <input type="hidden" name="session_id" value="<?php echo htmlspecialchars($session_id); ?>">
                    <input type="hidden" name="attendance_id" id="delete_attendance_id">

                    <p>Are you sure you want to remove the registration for "<strong id="delete_attendee_name"></strong>"?</p>
                    <p class="text-danger"><i class="bi bi-exclamation-triangle"></i> This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash"></i> Remove Registration
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Attendance records data for JavaScript
const attendanceRecords = <?php echo json_encode($attendance_records); ?>;

function toggleRegistrationType() {
    const memberRadio = document.getElementById('member_registration');
    const memberFields = document.getElementById('member_fields');
    const guestFields = document.getElementById('guest_fields');

    if (memberRadio.checked) {
        memberFields.style.display = 'block';
        guestFields.style.display = 'none';

        // Clear guest fields
        document.getElementById('guest_name').value = '';
        document.getElementById('guest_email').value = '';

        // Make member field required
        document.getElementById('member_id').required = true;
        document.getElementById('guest_name').required = false;
        document.getElementById('guest_email').required = false;
    } else {
        memberFields.style.display = 'none';
        guestFields.style.display = 'block';

        // Clear member field
        document.getElementById('member_id').value = '';

        // Make guest fields required
        document.getElementById('member_id').required = false;
        document.getElementById('guest_name').required = true;
        document.getElementById('guest_email').required = true;
    }
}

function editAttendance(attendanceId) {
    const record = attendanceRecords.find(r => r.id == attendanceId);
    if (!record) return;

    // Populate the edit form
    document.getElementById('edit_attendance_id').value = record.id;
    document.getElementById('edit_attendee_name').textContent = record.member_name || record.guest_name;
    document.getElementById('edit_attendance_status').value = record.attendance_status;
    document.getElementById('edit_notes').value = record.notes || '';

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('editAttendanceModal'));
    modal.show();
}

function deleteRegistration(attendanceId, attendeeName) {
    document.getElementById('delete_attendance_id').value = attendanceId;
    document.getElementById('delete_attendee_name').textContent = attendeeName;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('deleteRegistrationModal'));
    modal.show();
}

// Cross-page selection persistence for sessions
const SESSION_STORAGE_KEY = 'session_<?php echo $session_id; ?>_selected_attendees';

function getSessionStoredSelections() {
    try {
        const stored = sessionStorage.getItem(SESSION_STORAGE_KEY);
        return stored ? JSON.parse(stored) : [];
    } catch (e) {
        console.warn('Error reading stored session selections:', e);
        return [];
    }
}

function saveSessionStoredSelections(selectedIds) {
    try {
        sessionStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(selectedIds));
    } catch (e) {
        console.warn('Error saving session selections:', e);
    }
}

function clearSessionStoredSelections() {
    try {
        sessionStorage.removeItem(SESSION_STORAGE_KEY);
    } catch (e) {
        console.warn('Error clearing session selections:', e);
    }
}

function getAllSessionSelectedIds() {
    const storedSelections = getSessionStoredSelections();
    const currentPageSelections = [];

    document.querySelectorAll('.session-attendee-checkbox:checked').forEach(function(checkbox) {
        currentPageSelections.push(checkbox.value);
    });

    // Merge stored selections with current page selections
    const allSelections = [...new Set([...storedSelections, ...currentPageSelections])];

    // Remove any unchecked items from current page
    document.querySelectorAll('.session-attendee-checkbox:not(:checked)').forEach(function(checkbox) {
        const index = allSelections.indexOf(checkbox.value);
        if (index > -1) {
            allSelections.splice(index, 1);
        }
    });

    return allSelections;
}

// Session bulk operation functions
function selectAllAttendees() {
    document.querySelectorAll('.session-attendee-checkbox').forEach(function(checkbox) {
        checkbox.checked = true;
    });
    updateSelectedSessionCount();
    document.getElementById('select-all-session-checkbox').checked = true;
}

function selectNoneAttendees() {
    document.querySelectorAll('.session-attendee-checkbox').forEach(function(checkbox) {
        checkbox.checked = false;
    });
    updateSelectedSessionCount();
    document.getElementById('select-all-session-checkbox').checked = false;
}

function selectAllSessionPages() {
    // This would select all session attendees across all pages
    if (confirm('This will select ALL <?php echo $total_registered; ?> session attendees across all pages. Continue?')) {
        // Store a special flag to indicate all pages are selected
        sessionStorage.setItem(SESSION_STORAGE_KEY + '_all_selected', 'true');
        selectAllAttendees(); // Select current page
        updateSelectedSessionCount();
    }
}

function selectNoneSessionPages() {
    // Clear all selections across all pages
    clearSessionStoredSelections();
    sessionStorage.removeItem(SESSION_STORAGE_KEY + '_all_selected');
    selectNoneAttendees(); // Clear current page
    updateSelectedSessionCount();
}

function toggleSelectAllSession() {
    const selectAllCheckbox = document.getElementById('select-all-session-checkbox');
    const attendeeCheckboxes = document.querySelectorAll('.session-attendee-checkbox');

    attendeeCheckboxes.forEach(function(checkbox) {
        checkbox.checked = selectAllCheckbox.checked;
    });
    updateSelectedSessionCount();
}

function updateSelectedSessionCount() {
    // Save current page selections to storage
    const allSelectedIds = getAllSessionSelectedIds();
    saveSessionStoredSelections(allSelectedIds);

    // Check if all pages are selected
    const allPagesSelected = sessionStorage.getItem(SESSION_STORAGE_KEY + '_all_selected') === 'true';

    // Count selections
    const currentPageSelected = document.querySelectorAll('.session-attendee-checkbox:checked').length;
    const totalSelected = allPagesSelected ? <?php echo $total_registered; ?> : allSelectedIds.length;

    // Update display
    const countDisplay = allPagesSelected ?
        `ALL ${<?php echo $total_registered; ?>}` :
        `${totalSelected}`;

    document.getElementById('selected-attendees-count').textContent = countDisplay;

    // Enable/disable bulk action buttons
    const bulkButtons = ['bulk-selected-attended-btn', 'bulk-selected-no-show-btn'];
    bulkButtons.forEach(function(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = totalSelected === 0;
        }
    });

    // Update select all checkbox state for current page
    const totalCheckboxes = document.querySelectorAll('.session-attendee-checkbox').length;
    const selectAllCheckbox = document.getElementById('select-all-session-checkbox');
    if (currentPageSelected === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (currentPageSelected === totalCheckboxes) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }

    // Update cross-page selection info
    updateSessionCrossPageInfo(totalSelected, allPagesSelected);
}

function updateSessionCrossPageInfo(totalSelected, allPagesSelected) {
    let crossPageInfo = document.getElementById('session-cross-page-info');
    if (!crossPageInfo) {
        // Create cross-page info element
        crossPageInfo = document.createElement('div');
        crossPageInfo.id = 'session-cross-page-info';
        crossPageInfo.className = 'mt-2 small text-muted';
        document.getElementById('selected-attendees-count').parentNode.appendChild(crossPageInfo);
    }

    if (allPagesSelected) {
        crossPageInfo.innerHTML = '<i class="bi bi-info-circle"></i> All session attendees across all pages selected';
        crossPageInfo.className = 'mt-2 small text-warning';
    } else if (totalSelected > document.querySelectorAll('.session-attendee-checkbox:checked').length) {
        const crossPageCount = totalSelected - document.querySelectorAll('.session-attendee-checkbox:checked').length;
        crossPageInfo.innerHTML = `<i class="bi bi-info-circle"></i> +${crossPageCount} selected on other pages`;
        crossPageInfo.className = 'mt-2 small text-info';
    } else {
        crossPageInfo.innerHTML = '';
        crossPageInfo.className = 'mt-2 small text-muted';
    }
}

function bulkMarkSelectedAttended() {
    // Check if all pages are selected
    const allPagesSelected = sessionStorage.getItem(SESSION_STORAGE_KEY + '_all_selected') === 'true';

    if (allPagesSelected) {
        // Use global bulk operation for all pages
        if (!confirm(`Are you sure you want to mark ALL ${<?php echo $total_registered; ?>} session attendees as attended?`)) {
            return;
        }

        confirmBulkSessionOperation('bulk_mark_attended');
        return;
    }

    // Get all selected IDs across pages
    const allSelectedIds = getAllSessionSelectedIds();

    if (allSelectedIds.length === 0) {
        alert('Please select at least one attendee.');
        return;
    }

    const countText = allSelectedIds.length === 1 ? '1 attendee' : `${allSelectedIds.length} attendees`;

    if (!confirm(`Are you sure you want to mark ${countText} as attended?`)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '';

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'bulk_selected_attended';
    form.appendChild(actionInput);

    // Add all selected IDs (from all pages)
    allSelectedIds.forEach(function(id) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'selected_attendees[]';
        input.value = id;
        form.appendChild(input);
    });

    // Add flag to preserve selections after operation
    const preserveInput = document.createElement('input');
    preserveInput.type = 'hidden';
    preserveInput.name = 'preserve_selections';
    preserveInput.value = '1';
    form.appendChild(preserveInput);

    document.body.appendChild(form);
    form.submit();
}

function bulkMarkSelectedNoShow() {
    // Check if all pages are selected
    const allPagesSelected = sessionStorage.getItem(SESSION_STORAGE_KEY + '_all_selected') === 'true';

    if (allPagesSelected) {
        // Use global bulk operation for all pages
        if (!confirm(`Are you sure you want to mark ALL ${<?php echo $total_registered; ?>} session attendees as no-show?`)) {
            return;
        }

        confirmBulkSessionOperation('bulk_mark_no_show');
        return;
    }

    // Get all selected IDs across pages
    const allSelectedIds = getAllSessionSelectedIds();

    if (allSelectedIds.length === 0) {
        alert('Please select at least one attendee.');
        return;
    }

    const countText = allSelectedIds.length === 1 ? '1 attendee' : `${allSelectedIds.length} attendees`;

    if (!confirm(`Are you sure you want to mark ${countText} as no-show?`)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '';

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'bulk_selected_no_show';
    form.appendChild(actionInput);

    // Add all selected IDs (from all pages)
    allSelectedIds.forEach(function(id) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'selected_attendees[]';
        input.value = id;
        form.appendChild(input);
    });

    // Add flag to preserve selections after operation
    const preserveInput = document.createElement('input');
    preserveInput.type = 'hidden';
    preserveInput.name = 'preserve_selections';
    preserveInput.value = '1';
    form.appendChild(preserveInput);

    document.body.appendChild(form);
    form.submit();
}

function confirmBulkSessionOperation(action) {
    let message = '';
    let totalCount = <?php echo $total_registered; ?>;

    switch(action) {
        case 'bulk_mark_attended':
            message = `Are you sure you want to mark ALL ${totalCount} registered attendees as attended? This cannot be undone.`;
            break;
        case 'bulk_mark_no_show':
            message = `Are you sure you want to mark ALL ${totalCount} registered attendees as no-show? This cannot be undone.`;
            break;
        case 'bulk_reset_attendance':
            message = `Are you sure you want to reset attendance for ALL ${totalCount} attendees back to 'registered'? This cannot be undone.`;
            break;
    }

    if (!confirm(message)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '';

    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = action;
    form.appendChild(actionInput);

    document.body.appendChild(form);
    form.submit();
}

function restoreSessionSelections() {
    // Check if selections are preserved from URL (after bulk operation)
    const urlParams = new URLSearchParams(window.location.search);
    const preservedSelections = urlParams.get('preserve_selections');

    if (preservedSelections) {
        try {
            const preservedIds = JSON.parse(decodeURIComponent(preservedSelections));
            saveSessionStoredSelections(preservedIds);

            // Clean up URL
            urlParams.delete('preserve_selections');
            const newUrl = window.location.pathname + '?' + urlParams.toString();
            window.history.replaceState({}, '', newUrl);
        } catch (e) {
            console.warn('Error parsing preserved session selections:', e);
        }
    }

    const storedSelections = getSessionStoredSelections();
    const allPagesSelected = sessionStorage.getItem(SESSION_STORAGE_KEY + '_all_selected') === 'true';

    if (allPagesSelected) {
        // If all pages are selected, select all on current page
        document.querySelectorAll('.session-attendee-checkbox').forEach(function(checkbox) {
            checkbox.checked = true;
        });
    } else {
        // Restore individual selections for current page
        document.querySelectorAll('.session-attendee-checkbox').forEach(function(checkbox) {
            if (storedSelections.includes(checkbox.value)) {
                checkbox.checked = true;
            }
        });
    }

    updateSelectedSessionCount();
}

function addSessionCrossPageControls() {
    // Add cross-page selection controls
    const selectAllBtn = document.querySelector('button[onclick="selectAllAttendees()"]');
    const selectNoneBtn = document.querySelector('button[onclick="selectNoneAttendees()"]');

    if (selectAllBtn && selectNoneBtn) {
        const parentGroup = selectAllBtn.parentNode;

        // Add "Select All Pages" button
        const selectAllPagesBtn = document.createElement('button');
        selectAllPagesBtn.type = 'button';
        selectAllPagesBtn.className = 'btn btn-sm btn-warning';
        selectAllPagesBtn.onclick = selectAllSessionPages;
        selectAllPagesBtn.innerHTML = '<i class="bi bi-check-square-fill"></i> Select All Pages';
        selectAllPagesBtn.title = 'Select all <?php echo $total_registered; ?> session attendees across all pages';

        // Add "Clear All Pages" button
        const clearAllPagesBtn = document.createElement('button');
        clearAllPagesBtn.type = 'button';
        clearAllPagesBtn.className = 'btn btn-sm btn-outline-warning';
        clearAllPagesBtn.onclick = selectNoneSessionPages;
        clearAllPagesBtn.innerHTML = '<i class="bi bi-square"></i> Clear All Pages';
        clearAllPagesBtn.title = 'Clear all selections across all pages';

        // Insert after existing buttons
        parentGroup.appendChild(selectAllPagesBtn);
        parentGroup.appendChild(clearAllPagesBtn);
    }
}

// Quick individual attendance marking
function quickMarkAttendance(attendanceId, status) {
    const attendeeName = document.querySelector(`button[onclick="quickMarkAttendance(${attendanceId}, '${status}')"]`)
        .closest('tr').querySelector('td:nth-child(2)').textContent.trim();

    let confirmMessage = '';
    switch(status) {
        case 'attended':
            confirmMessage = `Mark "${attendeeName}" as attended?`;
            break;
        case 'no_show':
            confirmMessage = `Mark "${attendeeName}" as no-show?`;
            break;
        case 'registered':
            confirmMessage = `Reset "${attendeeName}" back to registered status?`;
            break;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '';

    // Add action
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'quick_mark_individual';
    form.appendChild(actionInput);

    // Add attendance ID
    const idInput = document.createElement('input');
    idInput.type = 'hidden';
    idInput.name = 'attendance_id';
    idInput.value = attendanceId;
    form.appendChild(idInput);

    // Add status
    const statusInput = document.createElement('input');
    statusInput.type = 'hidden';
    statusInput.name = 'attendance_status';
    statusInput.value = status;
    form.appendChild(statusInput);

    document.body.appendChild(form);
    form.submit();
}

// Initialize form validation
document.addEventListener('DOMContentLoaded', function() {
    // Set initial state
    toggleRegistrationType();
    restoreSessionSelections();
    addSessionCrossPageControls();
    updateSelectedSessionCount();
});
</script>

<?php include 'includes/footer.php'; ?>
