<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$message = '';
$error = '';

// Handle admin user creation
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_admin'])) {
    try {
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);
        $password = $_POST['password'];
        $full_name = trim($_POST['full_name']);
        
        // Validate input
        if (empty($username) || empty($email) || empty($password)) {
            throw new Exception('Username, email, and password are required.');
        }
        
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Please enter a valid email address.');
        }
        
        if (strlen($password) < 6) {
            throw new Exception('Password must be at least 6 characters long.');
        }
        
        // Check if username or email already exists
        $stmt = $pdo->prepare("SELECT id FROM admins WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        if ($stmt->fetch()) {
            throw new Exception('Username or email already exists.');
        }
        
        // Hash password
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        
        // Insert new admin
        $stmt = $pdo->prepare("
            INSERT INTO admins (username, email, password, full_name, created_at) 
            VALUES (?, ?, ?, ?, NOW())
        ");
        $stmt->execute([$username, $email, $hashed_password, $full_name]);
        
        $message = "Admin user '{$username}' created successfully!";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Handle bulk admin creation for testing
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['create_test_admins'])) {
    try {
        $test_admins = [
            ['username' => 'coordinator1', 'email' => '<EMAIL>', 'full_name' => 'Sarah Johnson', 'role' => 'Event Coordinator'],
            ['username' => 'coordinator2', 'email' => '<EMAIL>', 'full_name' => 'Michael Davis', 'role' => 'Event Coordinator'],
            ['username' => 'moderator1', 'email' => '<EMAIL>', 'full_name' => 'Emily Wilson', 'role' => 'Session Moderator'],
            ['username' => 'moderator2', 'email' => '<EMAIL>', 'full_name' => 'David Brown', 'role' => 'Session Moderator'],
            ['username' => 'staff1', 'email' => '<EMAIL>', 'full_name' => 'Lisa Anderson', 'role' => 'Staff'],
            ['username' => 'staff2', 'email' => '<EMAIL>', 'full_name' => 'James Miller', 'role' => 'Staff'],
            ['username' => 'organizer1', 'email' => '<EMAIL>', 'full_name' => 'Rachel Green', 'role' => 'Organizer'],
        ];
        
        $created_count = 0;
        $default_password = 'password123'; // Simple password for testing
        $hashed_password = password_hash($default_password, PASSWORD_DEFAULT);
        
        foreach ($test_admins as $admin) {
            // Check if user already exists
            $stmt = $pdo->prepare("SELECT id FROM admins WHERE username = ? OR email = ?");
            $stmt->execute([$admin['username'], $admin['email']]);
            
            if (!$stmt->fetch()) {
                // Create the admin user
                $stmt = $pdo->prepare("
                    INSERT INTO admins (username, email, password, full_name, created_at) 
                    VALUES (?, ?, ?, ?, NOW())
                ");
                $stmt->execute([$admin['username'], $admin['email'], $hashed_password, $admin['full_name']]);
                $created_count++;
            }
        }
        
        $message = "Created {$created_count} test admin users successfully! Default password: {$default_password}";
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Get existing admin users
$stmt = $pdo->query("SELECT id, username, email, full_name, created_at FROM admins ORDER BY created_at DESC");
$existing_admins = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Page title and header info
$page_title = 'Create Admin Users';
$page_header = 'Create Admin Users';
$page_description = 'Create additional admin users for role assignment testing';

// Include header
include 'includes/header.php';
?>

<style>
.admin-header {
    background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
    color: white;
    border-radius: 10px;
}
.user-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    transition: all 0.2s ease;
}
.user-card:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card admin-header">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-person-plus"></i> Create Admin Users
                        </h2>
                        <p class="text-white-50 mb-0">Create additional admin users for role assignment and testing</p>
                    </div>
                    <div>
                        <a href="setup_rbac_system.php" class="btn btn-outline-light">
                            <i class="bi bi-shield-check"></i> Back to RBAC
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Quick Test Users Creation -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Setup - Create Test Admin Users
                </h5>
            </div>
            <div class="card-body">
                <p>Create multiple test admin users quickly for role assignment testing:</p>
                <div class="row">
                    <div class="col-md-8">
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>coordinator1</strong> (Sarah Johnson)</span>
                                <span class="text-muted"><EMAIL></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>coordinator2</strong> (Michael Davis)</span>
                                <span class="text-muted"><EMAIL></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>moderator1</strong> (Emily Wilson)</span>
                                <span class="text-muted"><EMAIL></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>moderator2</strong> (David Brown)</span>
                                <span class="text-muted"><EMAIL></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>staff1</strong> (Lisa Anderson)</span>
                                <span class="text-muted"><EMAIL></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>staff2</strong> (James Miller)</span>
                                <span class="text-muted"><EMAIL></span>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span><strong>organizer1</strong> (Rachel Green)</span>
                                <span class="text-muted"><EMAIL></span>
                            </li>
                        </ul>
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="bi bi-info-circle"></i> 
                                All test users will have the password: <strong>password123</strong>
                            </small>
                        </div>
                    </div>
                    <div class="col-md-4 text-center">
                        <form method="POST">
                            <button type="submit" name="create_test_admins" class="btn btn-success btn-lg" 
                                    onclick="return confirm('Create 7 test admin users?')">
                                <i class="bi bi-people"></i> Create Test Users
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Manual Admin Creation -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-person-plus"></i> Create Individual Admin User
                </h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">Username *</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name">
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Password *</label>
                        <input type="password" class="form-control" id="password" name="password" required minlength="6">
                        <div class="form-text">Minimum 6 characters</div>
                    </div>
                    
                    <button type="submit" name="create_admin" class="btn btn-primary">
                        <i class="bi bi-person-plus"></i> Create Admin User
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-people"></i> Existing Admin Users (<?php echo count($existing_admins); ?>)
                </h5>
            </div>
            <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                <?php if (empty($existing_admins)): ?>
                    <p class="text-muted">No admin users found.</p>
                <?php else: ?>
                    <?php foreach ($existing_admins as $admin): ?>
                        <div class="user-card">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong><?php echo htmlspecialchars($admin['username']); ?></strong>
                                    <?php if ($admin['full_name']): ?>
                                        <br><small class="text-muted"><?php echo htmlspecialchars($admin['full_name']); ?></small>
                                    <?php endif; ?>
                                </div>
                                <div class="text-end">
                                    <small class="text-muted"><?php echo htmlspecialchars($admin['email']); ?></small>
                                    <br><small class="text-muted">ID: <?php echo $admin['id']; ?></small>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Information -->
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle"></i> About Admin User Management
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>How Admin Users Work:</h6>
                        <ul>
                            <li><strong>Admin Users</strong> are created through this interface or the registration system</li>
                            <li><strong>Roles</strong> are assigned separately through RBAC Management</li>
                            <li><strong>Login</strong> is done through the same login.php page for all users</li>
                            <li><strong>Dashboard Routing</strong> happens automatically based on assigned roles</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Next Steps:</h6>
                        <ol>
                            <li>Create admin users here (or use quick test users)</li>
                            <li>Go to <a href="setup_rbac_system.php">RBAC Management</a></li>
                            <li>Assign roles to the users</li>
                            <li>Test with <a href="system_test_dashboard.php">Role Simulation</a></li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
