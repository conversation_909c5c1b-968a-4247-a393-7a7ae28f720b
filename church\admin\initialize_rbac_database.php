<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$message = '';
$error = '';
$setup_complete = false;

// Handle RBAC database initialization
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['initialize_rbac'])) {
    try {
        // Start transaction
        $pdo->beginTransaction();
        
        // Create user_roles table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS user_roles (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                role_name VARCHAR(50) NOT NULL UNIQUE,
                role_display_name VARCHAR(100) NOT NULL,
                role_description TEXT,
                hierarchy_level INT(11) NOT NULL DEFAULT 0,
                dashboard_route VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                INDEX idx_hierarchy_level (hierarchy_level),
                INDEX idx_role_name (role_name)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Create permissions table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS permissions (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                permission_name VARCHAR(100) NOT NULL UNIQUE,
                permission_display_name VARCHAR(150) NOT NULL,
                permission_description TEXT,
                resource_type ENUM('event', 'session', 'user', 'system', 'report') NOT NULL,
                action_type ENUM('create', 'read', 'update', 'delete', 'manage', 'export') NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_resource_type (resource_type),
                INDEX idx_action_type (action_type)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Create role_permissions table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS role_permissions (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                role_id INT(11) NOT NULL,
                permission_id INT(11) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE KEY unique_role_permission (role_id, permission_id),
                FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
                FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
                INDEX idx_role_id (role_id),
                INDEX idx_permission_id (permission_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Create user_role_assignments table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS user_role_assignments (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                user_id INT(11) NOT NULL,
                role_id INT(11) NOT NULL,
                assigned_by INT(11) NOT NULL,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NULL,
                is_active TINYINT(1) DEFAULT 1,
                UNIQUE KEY unique_user_role (user_id, role_id),
                FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES admins(id) ON DELETE RESTRICT,
                INDEX idx_user_id (user_id),
                INDEX idx_role_id (role_id),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Create event_assignments table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS event_assignments (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                user_id INT(11) NOT NULL,
                event_id INT(11) NOT NULL,
                role_type ENUM('coordinator', 'organizer') NOT NULL,
                assigned_by INT(11) NOT NULL,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active TINYINT(1) DEFAULT 1,
                UNIQUE KEY unique_user_event_role (user_id, event_id, role_type),
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES admins(id) ON DELETE RESTRICT,
                INDEX idx_user_id (user_id),
                INDEX idx_event_id (event_id),
                INDEX idx_role_type (role_type),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Create session_assignments table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS session_assignments (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                user_id INT(11) NOT NULL,
                session_id INT(11) NOT NULL,
                assigned_by INT(11) NOT NULL,
                assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active TINYINT(1) DEFAULT 1,
                UNIQUE KEY unique_user_session (user_id, session_id),
                FOREIGN KEY (session_id) REFERENCES event_sessions(id) ON DELETE CASCADE,
                FOREIGN KEY (assigned_by) REFERENCES admins(id) ON DELETE RESTRICT,
                INDEX idx_user_id (user_id),
                INDEX idx_session_id (session_id),
                INDEX idx_is_active (is_active)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Create dashboard_access_log table
        $pdo->exec("
            CREATE TABLE IF NOT EXISTS dashboard_access_log (
                id INT(11) AUTO_INCREMENT PRIMARY KEY,
                user_id INT(11) NOT NULL,
                role_name VARCHAR(50) NOT NULL,
                dashboard_accessed VARCHAR(100) NOT NULL,
                access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address VARCHAR(45),
                user_agent TEXT,
                session_duration INT(11) DEFAULT 0,
                actions_performed INT(11) DEFAULT 0,
                INDEX idx_user_id (user_id),
                INDEX idx_role_name (role_name),
                INDEX idx_access_time (access_time)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
        
        // Insert default roles
        $pdo->exec("
            INSERT IGNORE INTO user_roles (role_name, role_display_name, role_description, hierarchy_level, dashboard_route) VALUES
            ('super_admin', 'Super Administrator', 'Complete oversight of all events, sessions, and user management', 1, 'super_admin_dashboard.php'),
            ('event_coordinator', 'Event Coordinator', 'Multi-session oversight for assigned events', 2, 'event_coordinator_dashboard.php'),
            ('organizer', 'Event Organizer', 'Event planning and setup tools', 3, 'organizer_dashboard.php'),
            ('session_moderator', 'Session Moderator', 'Individual session management for assigned sessions only', 4, 'session_moderator_dashboard.php'),
            ('staff', 'Check-in Staff', 'Limited access for check-in and basic attendance marking', 5, 'staff_dashboard.php')
        ");
        
        // Insert default permissions
        $pdo->exec("
            INSERT IGNORE INTO permissions (permission_name, permission_display_name, permission_description, resource_type, action_type) VALUES
            -- System permissions
            ('system.manage_all', 'Manage All System Functions', 'Complete system administration access', 'system', 'manage'),
            ('system.view_analytics', 'View System Analytics', 'Access to system-wide analytics and reports', 'system', 'read'),
            ('system.export_data', 'Export System Data', 'Export comprehensive system data', 'system', 'export'),
            
            -- User management permissions
            ('user.manage_all', 'Manage All Users', 'Create, update, delete all user accounts', 'user', 'manage'),
            ('user.view_all', 'View All Users', 'View all user accounts and roles', 'user', 'read'),
            ('user.assign_roles', 'Assign User Roles', 'Assign and modify user roles', 'user', 'update'),
            
            -- Event permissions
            ('event.manage_all', 'Manage All Events', 'Create, update, delete all events', 'event', 'manage'),
            ('event.manage_assigned', 'Manage Assigned Events', 'Manage only assigned events', 'event', 'update'),
            ('event.view_all', 'View All Events', 'View all events in the system', 'event', 'read'),
            ('event.view_assigned', 'View Assigned Events', 'View only assigned events', 'event', 'read'),
            ('event.create', 'Create Events', 'Create new events', 'event', 'create'),
            ('event.export_data', 'Export Event Data', 'Export event attendance and analytics', 'event', 'export'),
            
            -- Session permissions
            ('session.manage_all', 'Manage All Sessions', 'Create, update, delete all sessions', 'session', 'manage'),
            ('session.manage_assigned', 'Manage Assigned Sessions', 'Manage only assigned sessions', 'session', 'update'),
            ('session.view_all', 'View All Sessions', 'View all sessions in the system', 'session', 'read'),
            ('session.view_assigned', 'View Assigned Sessions', 'View only assigned sessions', 'session', 'read'),
            ('session.mark_attendance', 'Mark Session Attendance', 'Mark attendance for session participants', 'session', 'update'),
            ('session.export_data', 'Export Session Data', 'Export session attendance data', 'session', 'export'),
            
            -- Report permissions
            ('report.view_all', 'View All Reports', 'Access to all system reports', 'report', 'read'),
            ('report.view_assigned', 'View Assigned Reports', 'View reports for assigned events/sessions', 'report', 'read'),
            ('report.export', 'Export Reports', 'Export report data', 'report', 'export')
        ");
        
        // Assign permissions to roles
        // Super Admin gets all permissions
        $pdo->exec("
            INSERT IGNORE INTO role_permissions (role_id, permission_id)
            SELECT ur.id, p.id
            FROM user_roles ur, permissions p
            WHERE ur.role_name = 'super_admin'
        ");
        
        // Event Coordinator permissions
        $pdo->exec("
            INSERT IGNORE INTO role_permissions (role_id, permission_id)
            SELECT ur.id, p.id
            FROM user_roles ur, permissions p
            WHERE ur.role_name = 'event_coordinator'
            AND p.permission_name IN (
                'event.view_assigned', 'event.manage_assigned', 'event.export_data',
                'session.view_all', 'session.manage_all', 'session.mark_attendance', 'session.export_data',
                'report.view_assigned', 'report.export'
            )
        ");
        
        // Organizer permissions
        $pdo->exec("
            INSERT IGNORE INTO role_permissions (role_id, permission_id)
            SELECT ur.id, p.id
            FROM user_roles ur, permissions p
            WHERE ur.role_name = 'organizer'
            AND p.permission_name IN (
                'event.view_assigned', 'event.manage_assigned', 'event.create',
                'session.view_all', 'session.manage_all',
                'report.view_assigned'
            )
        ");
        
        // Session Moderator permissions
        $pdo->exec("
            INSERT IGNORE INTO role_permissions (role_id, permission_id)
            SELECT ur.id, p.id
            FROM user_roles ur, permissions p
            WHERE ur.role_name = 'session_moderator'
            AND p.permission_name IN (
                'event.view_assigned',
                'session.view_assigned', 'session.manage_assigned', 'session.mark_attendance',
                'report.view_assigned'
            )
        ");
        
        // Staff permissions
        $pdo->exec("
            INSERT IGNORE INTO role_permissions (role_id, permission_id)
            SELECT ur.id, p.id
            FROM user_roles ur, permissions p
            WHERE ur.role_name = 'staff'
            AND p.permission_name IN (
                'session.view_assigned', 'session.mark_attendance'
            )
        ");
        
        // Assign super_admin role to current user
        $current_admin_id = $_SESSION['admin_id'];
        $stmt = $pdo->prepare("SELECT id FROM user_roles WHERE role_name = 'super_admin'");
        $stmt->execute();
        $super_admin_role_id = $stmt->fetchColumn();
        
        if ($super_admin_role_id) {
            $stmt = $pdo->prepare("
                INSERT IGNORE INTO user_role_assignments (user_id, role_id, assigned_by) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$current_admin_id, $super_admin_role_id, $current_admin_id]);
        }
        
        // Commit transaction
        $pdo->commit();
        $setup_complete = true;
        $message = "RBAC system initialized successfully! All database tables created and you have been assigned Super Admin role.";

    } catch (Exception $e) {
        // Rollback transaction on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        $error = "Error initializing RBAC system: " . $e->getMessage();
        error_log("RBAC Initialization Error: " . $e->getMessage());
    }
}

// Page title and header info
$page_title = 'Initialize RBAC Database';
$page_header = 'Initialize RBAC Database';
$page_description = 'Create database tables for Role-Based Access Control system';

// Include header
include 'includes/header.php';
?>

<style>
.init-card {
    max-width: 800px;
    margin: 0 auto;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.success-animation {
    animation: pulse 2s infinite;
}
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>

<div class="container mt-5">
    <div class="row">
        <div class="col-md-12">
            <div class="card init-card">
                <div class="card-header text-center">
                    <h3><i class="bi bi-database-gear"></i> RBAC Database Initialization</h3>
                </div>
                <div class="card-body">
                    
                    <?php if ($setup_complete): ?>
                        <div class="text-center success-animation">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                            <h4 class="text-success mt-3">Setup Complete!</h4>
                            <p class="lead">The RBAC system has been successfully initialized.</p>
                            <div class="mt-4">
                                <a href="super_admin_dashboard.php" class="btn btn-success btn-lg">
                                    <i class="bi bi-speedometer2"></i> Go to Super Admin Dashboard
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        
                        <!-- Success/Error Messages -->
                        <?php if ($message): ?>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        
                        <div class="text-center mb-4">
                            <i class="bi bi-database-exclamation text-warning" style="font-size: 4rem;"></i>
                            <h4 class="mt-3">RBAC System Not Initialized</h4>
                            <p class="lead text-muted">The Role-Based Access Control system needs to be set up before you can use the hierarchical dashboard system.</p>
                        </div>
                        
                        <div class="alert alert-info">
                            <h5><i class="bi bi-info-circle"></i> What will be created:</h5>
                            <ul class="mb-0">
                                <li><strong>Database Tables:</strong> user_roles, permissions, role_permissions, user_role_assignments, event_assignments, session_assignments, dashboard_access_log</li>
                                <li><strong>Default Roles:</strong> Super Admin, Event Coordinator, Organizer, Session Moderator, Staff</li>
                                <li><strong>Permissions:</strong> Complete permission system for all resources and actions</li>
                                <li><strong>Your Access:</strong> You will be assigned Super Admin role automatically</li>
                            </ul>
                        </div>
                        
                        <form method="POST" class="text-center">
                            <button type="submit" name="initialize_rbac" class="btn btn-primary btn-lg" 
                                    onclick="return confirm('This will create the RBAC database tables and assign you Super Admin role. Continue?')">
                                <i class="bi bi-gear"></i> Initialize RBAC System
                            </button>
                        </form>
                        
                    <?php endif; ?>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<?php include 'includes/footer.php'; ?>
