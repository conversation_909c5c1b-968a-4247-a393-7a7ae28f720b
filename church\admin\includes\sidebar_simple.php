<?php
// Get the current page filename to highlight the active menu item
$current_page = basename($_SERVER['PHP_SELF']);

// Helper function to build admin URL consistently
if (!function_exists('admin_url_for')) {
    function admin_url_for($page) {
        if (defined('ADMIN_URL')) {
            return ADMIN_URL . '/' . $page;
        }
        return $page; // Fallback for pages in the same directory
    }
}

// Helper function to check if current page is active
if (!function_exists('is_active')) {
    function is_active($page_name) {
        global $current_page;
        return ($current_page == $page_name) ? 'active' : '';
    }
}

// Helper function to get member term
if (!function_exists('get_member_term')) {
    function get_member_term($plural = false) {
        $term = get_site_setting('member_term', 'Member');
        return $plural ? $term . 's' : $term;
    }
}

// SVG Icon Helper Function
if (!function_exists('get_svg_icon')) {
    function get_svg_icon($icon_name, $size = 16) {
        $icons = [
            'dashboard' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/></svg>',
            'users' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 7c0-2.21-1.79-4-4-4S8 4.79 8 7s1.79 4 4 4 4-1.79 4-4zm-4 6c-2.67 0-8 1.34-8 4v3h16v-3c0-2.66-5.33-4-8-4z"/></svg>',
            'user-plus' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M15 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm-9-2V7H4v3H1v2h3v3h2v-3h3v-2H6zm9 4c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>',
            'calendar' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3h-1V1h-2v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V8h14v11zM7 10h5v5H7z"/></svg>',
            'email' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/></svg>',
            'sms' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 9h12v2H6V9zm8 5H6v-2h8v2zm4-6H6V6h12v2z"/></svg>',
            'settings' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.82,11.69,4.82,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>',
            'chevron-left' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/></svg>',
            'chevron-right' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/></svg>',
            'menu' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/></svg>',
            'logout' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.59L17 17l5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/></svg>',
            'integrations' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>',
            'profile' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>',
            'brush' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M7 14c-1.66 0-3 1.34-3 3 0 1.31-1.16 2-2 2 .92 1.22 2.49 2 4 2 2.21 0 4-1.79 4-4 0-1.66-1.34-3-3-3zm13.71-9.37l-1.34-1.34c-.39-.39-1.02-.39-1.41 0L9 12.25 11.75 15l8.96-8.96c.39-.39.39-1.02 0-1.41z"/></svg>',
            'palette' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3c-4.97 0-9 4.03-9 9 0 4.97 4.03 9 9 9 .83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/></svg>',
            'group' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.01 3.01 0 0 0 17.1 7H16.9c-.8 0-1.54.37-2.01 1l-2.54 7.63H15v6h5zM12.5 11.5c.83 0 1.5-.67 1.5-1.5s-.67-1.5-1.5-1.5S11 9.17 11 10s.67 1.5 1.5 1.5zm1.5 1h-2c-.83 0-1.5.67-1.5 1.5v6h5v-6c0-.83-.67-1.5-1.5-1.5zM6 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5L10 8.37A3.01 3.01 0 0 0 7.1 7H6.9c-.8 0-1.54.37-2.01 1L2.5 16H5v6h5z"/></svg>',
            'gift' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-2 .89-2 2v4c0 1.11.89 2 2 2h1v6c0 1.11.89 2 2 2h10c1.11 0 2-.89 2-2v-6h1c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zm-5-2c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/></svg>',
            'template' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>',
            'chart' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M3.5 18.49l6-6.01 4 4L22 6.92l-1.41-1.41-7.09 7.97-4-4L2 16.99z"/></svg>',
            'analytics' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>',
            'dollar' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/></svg>',
            'table' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M10 10.02h5V21h-5zM17 21h3c1.1 0 2-.9 2-2v-9h-5v11zm3-18H5c-1.1 0-2 .9-2 2v3h19V5c0-1.1-.9-2-2-2zM3 19c0 1.1.9 2 2 2h3V10H3v9z"/></svg>',
            'check' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/></svg>',
            'tools' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M22.7 19l-9.1-9.1c.9-2.3.4-5-1.5-6.9-2-2-5-2.4-7.4-1.3L9 6 6 9 1.6 4.7C.4 7.1.9 10.1 2.9 12.1c1.9 1.9 4.6 2.4 6.9 1.5l9.1 9.1c.4.4 1 .4 1.4 0l2.3-2.3c.5-.4.5-1.1.1-1.4z"/></svg>',
            'bug' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 8h-2.81c-.45-.78-1.07-1.45-1.82-1.96L17 4.41 15.59 3l-2.17 2.17C12.96 5.06 12.49 5 12 5c-.49 0-.96.06-1.41.17L8.41 3 7 4.41l1.62 1.63C7.88 6.55 7.26 7.22 6.81 8H4v2h2.09c-.05.33-.09.66-.09 1v1H4v2h2v1c0 .34.04.67.09 1H4v2h2.81c1.04 1.79 2.97 3 5.19 3s4.15-1.21 5.19-3H20v-2h-2.09c.05-.33.09-.66.09-1v-1h2v-2h-2v-1c0-.34-.04-.67-.09-1H20V8z"/></svg>',
            'database' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zM4 9v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4s-8-1.79-8-4zM4 16v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4s-8-1.79-8-4z"/></svg>',
            'info' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/></svg>',
            'image' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>',
            'shield' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M10,17L6,13L7.41,11.59L10,14.17L16.59,7.58L18,9L10,17Z"/></svg>',
            'audit' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>',
            'attendance' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.01 3.01 0 0 0 17.1 7H16.9c-.8 0-1.54.37-2.01 1l-2.54 7.63H15v6h5z"/></svg>',
            'category' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>',
            'report' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/></svg>',
            'birthday-send' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/></svg>',
            'birthday-test' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
            'notification' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.9 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/></svg>',
            'auto-template' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
            'whatsapp' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488"/></svg>',
            'campaign' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/></svg>',
            'social' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zm4 18v-6h2.5l-2.54-7.63A3.01 3.01 0 0 0 17.1 7H16.9c-.8 0-1.54.37-2.01 1l-2.54 7.63H15v6h5z"/></svg>',
            'integration' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/></svg>',
            'payment' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M20 4H4c-1.11 0-1.99.9-1.99 2L2 18c0 1.1.89 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4v-6h16v6zm0-10H4V6h16v2z"/></svg>',
            'custom-field' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
            'upload' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/></svg>',
            'language' => '<svg width="' . $size . '" height="' . $size . '" viewBox="0 0 24 24" fill="currentColor"><path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"/></svg>'
        ];

        return isset($icons[$icon_name]) ? $icons[$icon_name] : $icons['settings'];
    }
}
?>

<!-- Sidebar -->
<div class="col-auto sidebar themed-sidebar" id="sidebar">
    <div class="sidebar-header">
        <?php
        // Get organization name for initials
        $organizationName = get_organization_name();
        $siteInitials = '';
        if (!empty($organizationName)) {
            $words = explode(' ', $organizationName);
            foreach ($words as $word) {
                if (!empty($word)) {
                    $siteInitials .= strtoupper(substr($word, 0, 1));
                }
            }
            // Limit to 3 characters max
            $siteInitials = substr($siteInitials, 0, 3);
        }
        if (empty($siteInitials)) {
            $siteInitials = 'CA'; // Default fallback
        }
        ?>
        
        <!-- Logo/Title Container -->
        <div class="logo-container">
            <div class="sidebar-logo-text">
                <span class="navbar-brand-text"><?php echo get_admin_title(); ?></span>
            </div>
            <div class="sidebar-initials-container" style="display: none;">
                <div class="sidebar-initials"><?php echo $siteInitials; ?></div>
            </div>
        </div>

        <!-- Toggle button for desktop -->
        <button class="sidebar-toggle-btn d-none d-md-block" id="sidebarToggleDesktop" title="Toggle Sidebar">
            <?php echo get_svg_icon('chevron-left', 18); ?>
        </button>

        <!-- Mobile toggle button -->
        <button class="d-md-none btn btn-sm btn-outline-light" id="sidebarToggle">
            <?php echo get_svg_icon('menu', 18); ?>
        </button>
    </div>
    
    <div class="sidebar-content">
        <ul class="nav flex-column">
            <!-- Dashboard -->
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('dashboard.php'); ?>" href="<?php echo admin_url_for('dashboard.php'); ?>" title="Dashboard">
                    <span class="nav-icon"><?php echo get_svg_icon('dashboard', 18); ?></span>
                    <span class="menu-text">Dashboard</span>
                </a>
            </li>

            <!-- Member Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('users', 18); ?></span>
                    <span class="menu-text"><?php echo get_member_term(); ?> Management</span>
                </span>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('members.php'); ?>" href="<?php echo admin_url_for('members.php'); ?>" title="<?php echo get_member_term(true); ?>">
                    <span class="nav-icon"><?php echo get_svg_icon('users', 18); ?></span>
                    <span class="menu-text"><?php echo get_member_term(true); ?></span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('add_member.php'); ?>" href="<?php echo admin_url_for('add_member.php'); ?>" title="Add <?php echo get_member_term(); ?>">
                    <span class="nav-icon"><?php echo get_svg_icon('user-plus', 18); ?></span>
                    <span class="menu-text">Add <?php echo get_member_term(); ?></span>
                </a>
            </li>

            <!-- Events Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Events Management</span>
                </span>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('events.php'); ?>" href="<?php echo admin_url_for('events.php'); ?>" title="Events">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Events</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('event_attendance.php'); ?>" href="<?php echo admin_url_for('event_attendance.php'); ?>" title="Event Attendance">
                    <span class="nav-icon"><?php echo get_svg_icon('attendance', 18); ?></span>
                    <span class="menu-text">Event Attendance</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('event_categories.php'); ?>" href="<?php echo admin_url_for('event_categories.php'); ?>" title="Event Categories">
                    <span class="nav-icon"><?php echo get_svg_icon('category', 18); ?></span>
                    <span class="menu-text">Event Categories</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('event_reports.php'); ?>" href="<?php echo admin_url_for('event_reports.php'); ?>" title="Event Reports">
                    <span class="nav-icon"><?php echo get_svg_icon('report', 18); ?></span>
                    <span class="menu-text">Event Reports</span>
                </a>
            </li>

            <!-- Email Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('email', 18); ?></span>
                    <span class="menu-text">Email Management</span>
                </span>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('bulk_email.php'); ?>" href="<?php echo admin_url_for('bulk_email.php'); ?>" title="Bulk Email">
                    <span class="nav-icon"><?php echo get_svg_icon('email', 18); ?></span>
                    <span class="menu-text">Bulk Email</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('email_scheduler.php'); ?>" href="<?php echo admin_url_for('email_scheduler.php'); ?>" title="Email Scheduler">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Email Scheduler</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('contacts.php'); ?>" href="<?php echo admin_url_for('contacts.php'); ?>" title="Contacts">
                    <span class="nav-icon"><?php echo get_svg_icon('users', 18); ?></span>
                    <span class="menu-text">Contacts</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('contact_groups.php'); ?>" href="<?php echo admin_url_for('contact_groups.php'); ?>" title="Contact Groups">
                    <span class="nav-icon"><?php echo get_svg_icon('group', 18); ?></span>
                    <span class="menu-text">Contact Groups</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('birthday.php'); ?>" href="<?php echo admin_url_for('birthday.php'); ?>" title="Birthday Messages">
                    <span class="nav-icon"><?php echo get_svg_icon('gift', 18); ?></span>
                    <span class="menu-text">Birthday Messages</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('send_birthday_emails.php'); ?>" href="<?php echo admin_url_for('send_birthday_emails.php'); ?>" title="Send Bulk Birthday Emails">
                    <span class="nav-icon"><?php echo get_svg_icon('birthday-send', 18); ?></span>
                    <span class="menu-text">Send Bulk Birthday Emails</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('test_birthday_email.php'); ?>" href="<?php echo admin_url_for('test_birthday_email.php'); ?>" title="Test Birthday Emails">
                    <span class="nav-icon"><?php echo get_svg_icon('birthday-test', 18); ?></span>
                    <span class="menu-text">Test Birthday Emails</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo is_active('send_birthday_notification.php'); ?>" href="<?php echo admin_url_for('send_birthday_notification.php'); ?>" title="Send Birthday Notifications">
                    <span class="nav-icon"><?php echo get_svg_icon('notification', 18); ?></span>
                    <span class="menu-text">Send Birthday Notifications</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('email_templates.php'); ?>" href="<?php echo admin_url_for('email_templates.php'); ?>" title="Email Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('template', 18); ?></span>
                    <span class="menu-text">Email Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('automated_templates.php'); ?>" href="<?php echo admin_url_for('automated_templates.php'); ?>" title="Automated Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('auto-template', 18); ?></span>
                    <span class="menu-text">Automated Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('whatsapp_templates.php'); ?>" href="<?php echo admin_url_for('whatsapp_templates.php'); ?>" title="WhatsApp Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('whatsapp', 18); ?></span>
                    <span class="menu-text">WhatsApp Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('whatsapp_messages.php'); ?>" href="<?php echo admin_url_for('whatsapp_messages.php'); ?>" title="WhatsApp Messages">
                    <span class="nav-icon"><?php echo get_svg_icon('whatsapp', 18); ?></span>
                    <span class="menu-text">WhatsApp Messages</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('email_analytics.php'); ?>" href="<?php echo admin_url_for('email_analytics.php'); ?>" title="Email Analytics">
                    <span class="nav-icon"><?php echo get_svg_icon('analytics', 18); ?></span>
                    <span class="menu-text">Email Analytics</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('about_shortcodes.php'); ?>" href="<?php echo admin_url_for('about_shortcodes.php'); ?>" title="About & Shortcodes">
                    <span class="nav-icon"><?php echo get_svg_icon('info', 18); ?></span>
                    <span class="menu-text">About & Shortcodes</span>
                </a>
            </li>

            <!-- SMS Management Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">SMS Management</span>
                </span>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('single_sms.php'); ?>" href="<?php echo admin_url_for('single_sms.php'); ?>" title="Single SMS">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">Single SMS</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('bulk_sms.php'); ?>" href="<?php echo admin_url_for('bulk_sms.php'); ?>" title="Bulk SMS">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">Bulk SMS</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_campaigns.php'); ?>" href="<?php echo admin_url_for('sms_campaigns.php'); ?>" title="SMS Campaigns">
                    <span class="nav-icon"><?php echo get_svg_icon('campaign', 18); ?></span>
                    <span class="menu-text">SMS Campaigns</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_templates.php'); ?>" href="<?php echo admin_url_for('sms_templates.php'); ?>" title="SMS Templates">
                    <span class="nav-icon"><?php echo get_svg_icon('template', 18); ?></span>
                    <span class="menu-text">SMS Templates</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_analytics.php'); ?>" href="<?php echo admin_url_for('sms_analytics.php'); ?>" title="SMS Analytics">
                    <span class="nav-icon"><?php echo get_svg_icon('analytics', 18); ?></span>
                    <span class="menu-text">SMS Analytics</span>
                </a>
            </li>

            <!-- Integrations Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('integration', 18); ?></span>
                    <span class="menu-text">Integrations</span>
                </span>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('calendar_integration.php'); ?>" href="<?php echo admin_url_for('calendar_integration.php'); ?>" title="Calendar Integration">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Calendar Integration</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('social_media.php'); ?>" href="<?php echo admin_url_for('social_media.php'); ?>" title="Social Media">
                    <span class="nav-icon"><?php echo get_svg_icon('social', 18); ?></span>
                    <span class="menu-text">Social Media</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('sms_integration.php'); ?>" href="<?php echo admin_url_for('sms_integration.php'); ?>" title="SMS Integration">
                    <span class="nav-icon"><?php echo get_svg_icon('sms', 18); ?></span>
                    <span class="menu-text">SMS Integration</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('payment_integration.php'); ?>" href="<?php echo admin_url_for('payment_integration.php'); ?>" title="Payment Integration">
                    <span class="nav-icon"><?php echo get_svg_icon('payment', 18); ?></span>
                    <span class="menu-text">Payment Integration</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('payment_tables.php'); ?>" href="<?php echo admin_url_for('payment_tables.php'); ?>" title="Payment Tables">
                    <span class="nav-icon"><?php echo get_svg_icon('table', 18); ?></span>
                    <span class="menu-text">Payment Tables</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('donations.php'); ?>" href="<?php echo admin_url_for('donations.php'); ?>" title="Donations">
                    <span class="nav-icon"><?php echo get_svg_icon('dollar', 18); ?></span>
                    <span class="menu-text">Donations</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('gift_management.php'); ?>" href="<?php echo admin_url_for('gift_management.php'); ?>" title="Gift Management">
                    <span class="nav-icon"><?php echo get_svg_icon('gift', 18); ?></span>
                    <span class="menu-text">Gift Management</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('enhanced_donate.php'); ?>" href="<?php echo admin_url_for('enhanced_donate.php'); ?>" title="Donation Form">
                    <span class="nav-icon"><?php echo get_svg_icon('heart', 18); ?></span>
                    <span class="menu-text">Donation</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('setup_enhanced_donations.php'); ?>" href="<?php echo admin_url_for('setup_enhanced_donations.php'); ?>" title="Setup Enhanced Donations">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">Setup Enhanced System</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('setup_birthday_gift_integration.php'); ?>" href="<?php echo admin_url_for('setup_birthday_gift_integration.php'); ?>" title="Birthday Gift Integration">
                    <span class="nav-icon"><?php echo get_svg_icon('calendar', 18); ?></span>
                    <span class="menu-text">Birthday Integration</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('check_payment_sdks.php'); ?>" href="<?php echo admin_url_for('check_payment_sdks.php'); ?>" title="Check Payment SDKs">
                    <span class="nav-icon"><?php echo get_svg_icon('check', 18); ?></span>
                    <span class="menu-text">Check Payment SDKs</span>
                </a>
            </li>

            <!-- Account Section -->
            <li class="nav-item nav-section-header">
                <span class="nav-section-title">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">Account</span>
                </span>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('settings.php'); ?>" href="<?php echo admin_url_for('settings.php'); ?>" title="Settings">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">Settings</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('appearance_settings.php'); ?>" href="<?php echo admin_url_for('appearance_settings.php'); ?>" title="Appearance">
                    <span class="nav-icon"><?php echo get_svg_icon('palette', 18); ?></span>
                    <span class="menu-text">Appearance</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('custom_fields.php'); ?>" href="<?php echo admin_url_for('custom_fields.php'); ?>" title="Custom Fields">
                    <span class="nav-icon"><?php echo get_svg_icon('custom-field', 18); ?></span>
                    <span class="menu-text">Custom Fields</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo is_active('branding_settings.php'); ?>" href="<?php echo admin_url_for('branding_settings.php'); ?>" title="Branding">
                    <span class="nav-icon"><?php echo get_svg_icon('brush', 18); ?></span>
                    <span class="menu-text">Branding</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('logo_management.php'); ?>" href="<?php echo admin_url_for('logo_management.php'); ?>" title="Logo Management">
                    <span class="nav-icon"><?php echo get_svg_icon('image', 18); ?></span>
                    <span class="menu-text">Logo Management</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('security_setup.php'); ?>" href="<?php echo admin_url_for('security_setup.php'); ?>" title="Security Setup">
                    <span class="nav-icon"><?php echo get_svg_icon('shield', 18); ?></span>
                    <span class="menu-text">Security Setup</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('security_audit.php'); ?>" href="<?php echo admin_url_for('security_audit.php'); ?>" title="Security Audit">
                    <span class="nav-icon"><?php echo get_svg_icon('audit', 18); ?></span>
                    <span class="menu-text">Security Audit</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('security_settings.php'); ?>" href="<?php echo admin_url_for('security_settings.php'); ?>" title="Security Settings">
                    <span class="nav-icon"><?php echo get_svg_icon('settings', 18); ?></span>
                    <span class="menu-text">Security Settings</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link <?php echo is_active('backup_management.php'); ?>" href="<?php echo admin_url_for('backup_management.php'); ?>" title="Database Backup">
                    <span class="nav-icon"><?php echo get_svg_icon('database', 18); ?></span>
                    <span class="menu-text">Database Backup</span>
                </a>
            </li>

            <li class="nav-item">
                <a class="nav-link <?php echo is_active('profile.php'); ?>" href="<?php echo admin_url_for('profile.php'); ?>" title="My Profile">
                    <span class="nav-icon"><?php echo get_svg_icon('profile', 18); ?></span>
                    <span class="menu-text">My Profile</span>
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="<?php echo admin_url_for('logout.php'); ?>" title="Logout">
                    <span class="nav-icon"><?php echo get_svg_icon('logout', 18); ?></span>
                    <span class="menu-text">Logout</span>
                </a>
            </li>
        </ul>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebarToggle');
    const desktopToggle = document.getElementById('sidebarToggleDesktop');
    const mainContent = document.querySelector('.main-content');
    const isMobile = window.innerWidth <= 768;

    // Mobile sidebar toggle
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function(e) {
            e.preventDefault();
            sidebar.classList.toggle('collapsed');
        });
    }

    // Desktop sidebar toggle
    if (!isMobile && desktopToggle) {
        // Check for user preference in localStorage
        if (localStorage.getItem('sidebarCollapsed') === 'true') {
            sidebar.classList.add('collapsed');
            if (mainContent) mainContent.classList.add('expanded');
            updateToggleIcon(true);
        }

        desktopToggle.addEventListener('click', function(e) {
            e.preventDefault();
            const isCollapsed = sidebar.classList.toggle('collapsed');
            
            if (mainContent) {
                mainContent.classList.toggle('expanded', isCollapsed);
            }
            
            updateToggleIcon(isCollapsed);
            localStorage.setItem('sidebarCollapsed', isCollapsed ? 'true' : 'false');
        });
    }

    function updateToggleIcon(isCollapsed) {
        if (desktopToggle) {
            const iconHtml = isCollapsed ? 
                '<?php echo addslashes(get_svg_icon('chevron-right', 18)); ?>' : 
                '<?php echo addslashes(get_svg_icon('chevron-left', 18)); ?>';
            desktopToggle.innerHTML = iconHtml;
        }
    }
});
</script>
