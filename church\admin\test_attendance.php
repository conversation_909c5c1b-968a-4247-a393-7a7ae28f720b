<?php
// Simple test file to check if our attendance page works
session_start();
$_SESSION['admin_id'] = 1; // Set test admin ID

// Include the configuration file
require_once '../config.php';

// Database connection
$conn = $pdo;

echo "<h1>Testing Attendance System</h1>";

try {
    // Test database connection
    echo "<h2>Database Connection: OK</h2>";
    
    // Check if events table exists and has data
    $stmt = $conn->query("SELECT COUNT(*) as count FROM events");
    $event_count = $stmt->fetchColumn();
    echo "<p>Events in database: {$event_count}</p>";
    
    if ($event_count > 0) {
        // Get first event
        $stmt = $conn->query("SELECT id, title FROM events LIMIT 1");
        $event = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>First event: ID {$event['id']} - {$event['title']}</p>";
        
        // Check for RSVPs
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM event_rsvps WHERE event_id = ?");
        $stmt->execute([$event['id']]);
        $rsvp_count = $stmt->fetchColumn();
        echo "<p>RSVPs for this event: {$rsvp_count}</p>";
        
        // Create test data if no RSVPs exist
        if ($rsvp_count == 0) {
            echo "<p>No RSVPs found. Creating test data...</p>";

            // Create test members if they don't exist
            for ($i = 1; $i <= 10; $i++) {
                $stmt = $conn->prepare("INSERT IGNORE INTO members (id, full_name, email, phone_number, status) VALUES (?, ?, ?, ?, 'active')");
                $stmt->execute([$i, "Test Member {$i}", "member{$i}@test.com", "123-456-789{$i}"]);

                // Create RSVP
                $stmt = $conn->prepare("INSERT IGNORE INTO event_rsvps (event_id, user_id, status) VALUES (?, ?, 'attending')");
                $stmt->execute([$event['id'], $i]);
            }

            // Create test guest RSVPs
            for ($i = 1; $i <= 5; $i++) {
                $stmt = $conn->prepare("INSERT IGNORE INTO event_rsvps_guests (event_id, guest_name, guest_email, status) VALUES (?, ?, ?, 'attending')");
                $stmt->execute([$event['id'], "Test Guest {$i}", "guest{$i}@test.com"]);
            }

            echo "<p>Created 10 member RSVPs and 5 guest RSVPs for event {$event['id']}</p>";
        }

        // Test link to attendance page
        echo "<p><a href='event_attendance_detail.php?event_id={$event['id']}&test=1'>Test Attendance Page</a></p>";
    } else {
        echo "<p>No events found. Creating test event...</p>";
        
        // Create test event
        $stmt = $conn->prepare("INSERT INTO events (title, description, event_date, location, created_by) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            'Test Event for Attendance',
            'This is a test event to test the bulk attendance functionality',
            date('Y-m-d H:i:s', strtotime('+1 week')),
            'Test Location',
            1
        ]);
        $event_id = $conn->lastInsertId();
        echo "<p>Created test event with ID: {$event_id}</p>";
        
        // Create test RSVPs
        for ($i = 1; $i <= 10; $i++) {
            // Create test member if not exists
            $stmt = $conn->prepare("INSERT IGNORE INTO members (id, full_name, email, phone_number, status) VALUES (?, ?, ?, ?, 'active')");
            $stmt->execute([$i, "Test Member {$i}", "member{$i}@test.com", "123-456-789{$i}"]);
            
            // Create RSVP
            $stmt = $conn->prepare("INSERT IGNORE INTO event_rsvps (event_id, user_id, status) VALUES (?, ?, 'attending')");
            $stmt->execute([$event_id, $i]);
        }
        
        // Create test guest RSVPs
        for ($i = 1; $i <= 5; $i++) {
            $stmt = $conn->prepare("INSERT IGNORE INTO event_rsvps_guests (event_id, guest_name, guest_email, status) VALUES (?, ?, ?, 'attending')");
            $stmt->execute([$event_id, "Test Guest {$i}", "guest{$i}@test.com"]);
        }
        
        echo "<p>Created 10 member RSVPs and 5 guest RSVPs</p>";
        echo "<p><a href='event_attendance_detail.php?event_id={$event_id}&test=1'>Test Attendance Page</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
