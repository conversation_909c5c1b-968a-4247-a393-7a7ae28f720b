<?php
session_start();

// Check if user is logged in as admin
if (!isset($_SESSION['admin_id'])) {
    header("Location: login.php");
    exit();
}

// Include the configuration file
require_once '../config.php';

$event_id = $_GET['event_id'] ?? '';
if (empty($event_id)) {
    header("Location: events.php");
    exit();
}

$message = '';
$error = '';

// Get event details
try {
    $stmt = $pdo->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$event) {
        header("Location: events.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error loading event: " . $e->getMessage();
}

// Handle POST requests for cross-session operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !empty($_POST['action'])) {
    try {
        $pdo->beginTransaction();
        
        if ($_POST['action'] === 'cross_session_bulk_mark') {
            $selected_sessions = $_POST['selected_sessions'] ?? [];
            $selected_attendees = $_POST['selected_attendees'] ?? [];
            $attendance_status = $_POST['attendance_status'] ?? '';
            
            if (empty($selected_sessions) || empty($selected_attendees) || empty($attendance_status)) {
                throw new Exception("Please select sessions, attendees, and attendance status.");
            }
            
            $affected_count = 0;
            
            foreach ($selected_sessions as $session_id) {
                foreach ($selected_attendees as $attendee_id) {
                    // Check if this attendee is registered for this session
                    $stmt = $pdo->prepare("
                        SELECT id FROM session_attendance
                        WHERE session_id = ? AND
                        (member_id = ? OR (member_id IS NULL AND CONCAT('guest_', id) = ?))
                    ");
                    $stmt->execute([$session_id, $attendee_id, $attendee_id]);
                    $attendance_record = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($attendance_record) {
                        // Update existing attendance record
                        $stmt = $pdo->prepare("
                            UPDATE session_attendance
                            SET attendance_status = ?,
                                attendance_date = CASE WHEN ? = 'attended' THEN NOW() ELSE attendance_date END
                            WHERE id = ?
                        ");
                        $stmt->execute([$attendance_status, $attendance_status, $attendance_record['id']]);
                        $affected_count++;
                    } else {
                        // Create new attendance record if attendee is not registered
                        if (strpos($attendee_id, 'guest_') === 0) {
                            // Guest attendee
                            $guest_id = str_replace('guest_', '', $attendee_id);
                            $stmt = $pdo->prepare("
                                SELECT guest_name, guest_email FROM event_rsvps_guests
                                WHERE id = ? AND event_id = ?
                            ");
                            $stmt->execute([$guest_id, $event_id]);
                            $guest = $stmt->fetch(PDO::FETCH_ASSOC);

                            if ($guest) {
                                $stmt = $pdo->prepare("
                                    INSERT INTO session_attendance
                                    (session_id, guest_name, guest_email, attendance_status, registration_date, attendance_date)
                                    VALUES (?, ?, ?, ?, NOW(), CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                                ");
                                $stmt->execute([$session_id, $guest['guest_name'], $guest['guest_email'], $attendance_status, $attendance_status]);
                                $affected_count++;
                            }
                        } else {
                            // Member attendee
                            $stmt = $pdo->prepare("
                                INSERT INTO session_attendance
                                (session_id, member_id, attendance_status, registration_date, attendance_date)
                                VALUES (?, ?, ?, NOW(), CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                            ");
                            $stmt->execute([$session_id, $attendee_id, $attendance_status, $attendance_status]);
                            $affected_count++;
                        }
                    }
                }
            }
            
            $pdo->commit();
            $session_count = count($selected_sessions);
            $attendee_count = count($selected_attendees);
            $status_text = ucfirst(str_replace('_', ' ', $attendance_status));
            $message = "Successfully marked {$attendee_count} attendees as '{$status_text}' across {$session_count} sessions. Total updates: {$affected_count}";

        } elseif ($_POST['action'] === 'copy_session_attendance') {
            $source_session = $_POST['source_session'] ?? '';
            $target_sessions = $_POST['target_sessions'] ?? [];
            $copy_mode = $_POST['copy_mode'] ?? 'attended_only';

            if (empty($source_session) || empty($target_sessions)) {
                throw new Exception("Please select source session and target sessions.");
            }

            // Get attendance from source session
            $where_clause = $copy_mode === 'attended_only' ? "AND attendance_status = 'attended'" : "";
            $stmt = $pdo->prepare("
                SELECT member_id, guest_name, guest_email, attendance_status
                FROM session_attendance
                WHERE session_id = ? {$where_clause}
            ");
            $stmt->execute([$source_session]);
            $source_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $copied_count = 0;
            
            foreach ($target_sessions as $target_session) {
                foreach ($source_attendees as $attendee) {
                    // Check if already exists
                    if ($attendee['member_id']) {
                        $stmt = $pdo->prepare("
                            SELECT id FROM session_attendance
                            WHERE session_id = ? AND member_id = ?
                        ");
                        $stmt->execute([$target_session, $attendee['member_id']]);
                    } else {
                        $stmt = $pdo->prepare("
                            SELECT id FROM session_attendance
                            WHERE session_id = ? AND guest_name = ? AND guest_email = ?
                        ");
                        $stmt->execute([$target_session, $attendee['guest_name'], $attendee['guest_email']]);
                    }

                    if (!$stmt->fetch()) {
                        // Insert new attendance record
                        $stmt = $pdo->prepare("
                            INSERT INTO session_attendance
                            (session_id, member_id, guest_name, guest_email, attendance_status, registration_date, attendance_date)
                            VALUES (?, ?, ?, ?, ?, NOW(), CASE WHEN ? = 'attended' THEN NOW() ELSE NULL END)
                        ");
                        $stmt->execute([
                            $target_session,
                            $attendee['member_id'],
                            $attendee['guest_name'],
                            $attendee['guest_email'],
                            $attendee['attendance_status'],
                            $attendee['attendance_status']
                        ]);
                        $copied_count++;
                    }
                }
            }

            $pdo->commit();
            $target_count = count($target_sessions);
            $source_count = count($source_attendees);
            $message = "Successfully copied {$source_count} attendees from source session to {$target_count} target sessions. Total new records: {$copied_count}";
        }

    } catch (Exception $e) {
        $pdo->rollBack();
        $error = $e->getMessage();
    }
}

// Get all sessions for this event
$stmt = $pdo->prepare("
    SELECT s.*,
           COUNT(sa.id) as registered_count,
           COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as attended_count,
           COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as no_show_count
    FROM event_sessions s
    LEFT JOIN session_attendance sa ON s.id = sa.session_id
    WHERE s.event_id = ? AND s.status = 'active'
    GROUP BY s.id
    ORDER BY s.start_datetime
");
$stmt->execute([$event_id]);
$sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all event attendees (members + guests)
// First check what columns exist in event_rsvps table
try {
    $columns_stmt = $pdo->query("SHOW COLUMNS FROM event_rsvps");
    $columns = $columns_stmt->fetchAll(PDO::FETCH_COLUMN);
    $member_id_column = in_array('user_id', $columns) ? 'user_id' : 'member_id';
    $actually_attended_exists = in_array('actually_attended', $columns);

    // Build the member query based on available columns
    $member_select = $actually_attended_exists ? "r.actually_attended as event_attended" : "NULL as event_attended";

    $stmt = $pdo->prepare("
        SELECT
            r.id as rsvp_id,
            r.$member_id_column as member_id,
            m.full_name as name,
            m.email,
            'member' as type,
            $member_select
        FROM event_rsvps r
        JOIN members m ON r.$member_id_column = m.id
        WHERE r.event_id = ? AND r.status = 'attending'

        UNION ALL

        SELECT
            CONCAT('guest_', g.id) as rsvp_id,
            NULL as member_id,
            g.guest_name as name,
            g.guest_email as email,
            'guest' as type,
            " . ($actually_attended_exists ? "g.actually_attended" : "NULL") . " as event_attended
        FROM event_rsvps_guests g
        WHERE g.event_id = ? AND g.status = 'attending'

        ORDER BY name
    ");
    $stmt->execute([$event_id, $event_id]);
    $all_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    // If there's an error with the query, fall back to a simpler approach
    error_log("Error getting attendees: " . $e->getMessage());

    // Try to get just members first
    try {
        $stmt = $pdo->prepare("
            SELECT
                r.id as rsvp_id,
                r.id as member_id,
                'Unknown Member' as name,
                '<EMAIL>' as email,
                'member' as type,
                NULL as event_attended
            FROM event_rsvps r
            WHERE r.event_id = ? AND r.status = 'attending'
            LIMIT 0
        ");
        $stmt->execute([$event_id]);
        $all_attendees = $stmt->fetchAll(PDO::FETCH_ASSOC);

        $error = "Database schema issue detected. Please check event_rsvps table structure.";
    } catch (PDOException $e2) {
        $all_attendees = [];
        $error = "Unable to load attendees: " . $e2->getMessage();
    }
}

// Page title and header info
$page_title = 'Cross-Session Attendance Management';
$page_header = 'Cross-Session Attendance Management';
$page_description = 'Manage attendance across multiple sessions for complex events';

// Include header
include 'includes/header.php';
?>

<style>
.session-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}
.session-card.selected {
    border-color: #0d6efd;
    background-color: #f8f9ff;
}
.attendee-row {
    cursor: pointer;
    transition: background-color 0.2s ease;
}
.attendee-row:hover {
    background-color: #f8f9fa;
}
.attendee-row.selected {
    background-color: #e3f2fd;
}
.stats-badge {
    font-size: 0.75rem;
}
</style>

<!-- Header -->
<div class="row mb-4">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2><i class="bi bi-diagram-3"></i> Cross-Session Attendance Management</h2>
                        <p class="text-muted mb-0">Event: <strong><?php echo htmlspecialchars($event['title']); ?></strong></p>
                        <small class="text-muted"><?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?> • <?php echo htmlspecialchars($event['location']); ?></small>
                    </div>
                    <div>
                        <a href="event_attendance_detail.php?event_id=<?php echo $event_id; ?>" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-left"></i> Back to Event Attendance
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Success/Error Messages -->
        <?php if ($message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <!-- Quick Stats -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row text-center">
                            <div class="col-md-3">
                                <h4 class="text-primary"><?php echo count($sessions); ?></h4>
                                <small class="text-muted">Total Sessions</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-success"><?php echo count($all_attendees); ?></h4>
                                <small class="text-muted">Event Attendees</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-info"><?php echo array_sum(array_column($sessions, 'registered_count')); ?></h4>
                                <small class="text-muted">Total Session Registrations</small>
                            </div>
                            <div class="col-md-3">
                                <h4 class="text-warning"><?php echo array_sum(array_column($sessions, 'attended_count')); ?></h4>
                                <small class="text-muted">Total Session Attendance</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cross-Session Operations -->
        <div class="row">
            <!-- Sessions Selection -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-calendar-event"></i> Select Sessions
                            <span class="badge bg-primary ms-2" id="selected-sessions-count">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="selectAllSessions()">
                                <i class="bi bi-check-square"></i> Select All
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNoSessions()">
                                <i class="bi bi-square"></i> Select None
                            </button>
                        </div>

                        <div class="session-list" style="max-height: 400px; overflow-y: auto;">
                            <?php foreach ($sessions as $session): ?>
                                <div class="session-card card mb-2" data-session-id="<?php echo $session['id']; ?>">
                                    <div class="card-body p-3">
                                        <div class="form-check">
                                            <input class="form-check-input session-checkbox" type="checkbox"
                                                   value="<?php echo $session['id']; ?>"
                                                   id="session_<?php echo $session['id']; ?>"
                                                   onchange="updateSessionSelection()">
                                            <label class="form-check-label w-100" for="session_<?php echo $session['id']; ?>">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <h6 class="mb-1"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                                        <small class="text-muted">
                                                            <?php echo date('M j, Y g:i A', strtotime($session['start_datetime'])); ?>
                                                            <?php if ($session['location']): ?>
                                                                • <?php echo htmlspecialchars($session['location']); ?>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-info stats-badge"><?php echo $session['registered_count']; ?> reg</span>
                                                        <span class="badge bg-success stats-badge"><?php echo $session['attended_count']; ?> att</span>
                                                        <span class="badge bg-warning stats-badge"><?php echo $session['no_show_count']; ?> no-show</span>
                                                    </div>
                                                </div>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Attendees Selection -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-people"></i> Select Attendees
                            <span class="badge bg-success ms-2" id="selected-attendees-count">0</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <button type="button" class="btn btn-sm btn-outline-success" onclick="selectAllAttendees()">
                                        <i class="bi bi-check-square"></i> Select All
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="selectNoAttendees()">
                                        <i class="bi bi-square"></i> Select None
                                    </button>
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control form-control-sm"
                                           placeholder="Search attendees..."
                                           id="attendee-search"
                                           onkeyup="filterAttendees()">
                                </div>
                            </div>
                        </div>

                        <div class="attendee-list" style="max-height: 400px; overflow-y: auto;">
                            <?php foreach ($all_attendees as $attendee): ?>
                                <div class="attendee-row p-2 border-bottom"
                                     data-attendee-id="<?php echo $attendee['rsvp_id']; ?>"
                                     data-name="<?php echo strtolower($attendee['name']); ?>"
                                     onclick="toggleAttendeeSelection('<?php echo $attendee['rsvp_id']; ?>')">
                                    <div class="form-check">
                                        <input class="form-check-input attendee-checkbox" type="checkbox"
                                               value="<?php echo $attendee['rsvp_id']; ?>"
                                               id="attendee_<?php echo $attendee['rsvp_id']; ?>"
                                               onchange="updateAttendeeSelection()">
                                        <label class="form-check-label w-100" for="attendee_<?php echo $attendee['rsvp_id']; ?>">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <strong><?php echo htmlspecialchars($attendee['name']); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($attendee['email']); ?></small>
                                                </div>
                                                <div class="text-end">
                                                    <span class="badge bg-<?php echo $attendee['type'] === 'member' ? 'primary' : 'secondary'; ?>">
                                                        <?php echo ucfirst($attendee['type']); ?>
                                                    </span>
                                                    <?php if ($attendee['event_attended'] === '1'): ?>
                                                        <span class="badge bg-success">Event Attended</span>
                                                    <?php elseif ($attendee['event_attended'] === '0'): ?>
                                                        <span class="badge bg-danger">Event No-Show</span>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bulk Operations -->
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightning"></i> Cross-Session Bulk Operations
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Cross-Session Bulk Mark -->
                        <div class="row mb-4">
                            <div class="col-md-12">
                                <h6><i class="bi bi-check-circle"></i> Bulk Mark Attendance</h6>
                                <p class="text-muted small">Mark selected attendees with the same status across selected sessions.</p>

                                <form method="POST" onsubmit="return confirmCrossSessionBulk()">
                                    <input type="hidden" name="action" value="cross_session_bulk_mark">
                                    <input type="hidden" name="selected_sessions" id="bulk-selected-sessions">
                                    <input type="hidden" name="selected_attendees" id="bulk-selected-attendees">

                                    <div class="row align-items-end">
                                        <div class="col-md-4">
                                            <label class="form-label">Attendance Status</label>
                                            <select class="form-select" name="attendance_status" required>
                                                <option value="">Select Status</option>
                                                <option value="attended">Attended</option>
                                                <option value="no_show">No-Show</option>
                                                <option value="registered">Reset to Registered</option>
                                            </select>
                                        </div>
                                        <div class="col-md-8">
                                            <button type="submit" class="btn btn-primary" id="bulk-mark-btn" disabled>
                                                <i class="bi bi-check-all"></i> Apply to Selected Sessions & Attendees
                                            </button>
                                            <span class="ms-3 text-muted" id="bulk-operation-summary">
                                                Select sessions and attendees to enable bulk operations
                                            </span>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <hr>

                        <!-- Copy Session Attendance -->
                        <div class="row">
                            <div class="col-md-12">
                                <h6><i class="bi bi-copy"></i> Copy Session Attendance</h6>
                                <p class="text-muted small">Copy attendance from one session to other sessions.</p>

                                <form method="POST" onsubmit="return confirmCopyAttendance()">
                                    <input type="hidden" name="action" value="copy_session_attendance">

                                    <div class="row align-items-end">
                                        <div class="col-md-3">
                                            <label class="form-label">Source Session</label>
                                            <select class="form-select" name="source_session" required>
                                                <option value="">Select Source</option>
                                                <?php foreach ($sessions as $session): ?>
                                                    <option value="<?php echo $session['id']; ?>">
                                                        <?php echo htmlspecialchars($session['session_title']); ?>
                                                        (<?php echo $session['attended_count']; ?> attended)
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Copy Mode</label>
                                            <select class="form-select" name="copy_mode">
                                                <option value="attended_only">Attended Only</option>
                                                <option value="all_statuses">All Statuses</option>
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Target Sessions</label>
                                            <input type="hidden" name="target_sessions" id="copy-target-sessions">
                                            <div class="form-control" style="height: auto; min-height: 38px;">
                                                <small class="text-muted" id="copy-target-summary">
                                                    Select sessions above
                                                </small>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <button type="submit" class="btn btn-warning" id="copy-attendance-btn" disabled>
                                                <i class="bi bi-copy"></i> Copy Attendance
                                            </button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
    // Session selection functions
    function selectAllSessions() {
        document.querySelectorAll('.session-checkbox').forEach(function(checkbox) {
            checkbox.checked = true;
            checkbox.closest('.session-card').classList.add('selected');
        });
        updateSessionSelection();
    }

    function selectNoSessions() {
        document.querySelectorAll('.session-checkbox').forEach(function(checkbox) {
            checkbox.checked = false;
            checkbox.closest('.session-card').classList.remove('selected');
        });
        updateSessionSelection();
    }

    function updateSessionSelection() {
        const selectedSessions = document.querySelectorAll('.session-checkbox:checked');
        const count = selectedSessions.length;

        document.getElementById('selected-sessions-count').textContent = count;

        // Update visual state
        document.querySelectorAll('.session-checkbox').forEach(function(checkbox) {
            if (checkbox.checked) {
                checkbox.closest('.session-card').classList.add('selected');
            } else {
                checkbox.closest('.session-card').classList.remove('selected');
            }
        });

        updateBulkOperationState();
        updateCopyOperationState();
    }

    // Attendee selection functions
    function selectAllAttendees() {
        document.querySelectorAll('.attendee-checkbox:not([style*="display: none"])').forEach(function(checkbox) {
            if (!checkbox.closest('.attendee-row').style.display.includes('none')) {
                checkbox.checked = true;
                checkbox.closest('.attendee-row').classList.add('selected');
            }
        });
        updateAttendeeSelection();
    }

    function selectNoAttendees() {
        document.querySelectorAll('.attendee-checkbox').forEach(function(checkbox) {
            checkbox.checked = false;
            checkbox.closest('.attendee-row').classList.remove('selected');
        });
        updateAttendeeSelection();
    }

    function toggleAttendeeSelection(attendeeId) {
        const checkbox = document.getElementById('attendee_' + attendeeId);
        checkbox.checked = !checkbox.checked;
        updateAttendeeSelection();
    }

    function updateAttendeeSelection() {
        const selectedAttendees = document.querySelectorAll('.attendee-checkbox:checked');
        const count = selectedAttendees.length;

        document.getElementById('selected-attendees-count').textContent = count;

        // Update visual state
        document.querySelectorAll('.attendee-checkbox').forEach(function(checkbox) {
            if (checkbox.checked) {
                checkbox.closest('.attendee-row').classList.add('selected');
            } else {
                checkbox.closest('.attendee-row').classList.remove('selected');
            }
        });

        updateBulkOperationState();
    }

    // Search functionality
    function filterAttendees() {
        const searchTerm = document.getElementById('attendee-search').value.toLowerCase();

        document.querySelectorAll('.attendee-row').forEach(function(row) {
            const name = row.getAttribute('data-name');
            if (name.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // Bulk operation state management
    function updateBulkOperationState() {
        const selectedSessions = document.querySelectorAll('.session-checkbox:checked');
        const selectedAttendees = document.querySelectorAll('.attendee-checkbox:checked');

        const sessionCount = selectedSessions.length;
        const attendeeCount = selectedAttendees.length;

        const bulkBtn = document.getElementById('bulk-mark-btn');
        const summary = document.getElementById('bulk-operation-summary');

        if (sessionCount > 0 && attendeeCount > 0) {
            bulkBtn.disabled = false;
            summary.textContent = `Ready to apply to ${attendeeCount} attendees across ${sessionCount} sessions`;
            summary.className = 'ms-3 text-success';
        } else {
            bulkBtn.disabled = true;
            if (sessionCount === 0 && attendeeCount === 0) {
                summary.textContent = 'Select sessions and attendees to enable bulk operations';
            } else if (sessionCount === 0) {
                summary.textContent = `${attendeeCount} attendees selected - select sessions to continue`;
            } else {
                summary.textContent = `${sessionCount} sessions selected - select attendees to continue`;
            }
            summary.className = 'ms-3 text-muted';
        }

        // Update hidden form fields
        const sessionIds = Array.from(selectedSessions).map(cb => cb.value);
        const attendeeIds = Array.from(selectedAttendees).map(cb => cb.value);

        document.getElementById('bulk-selected-sessions').value = JSON.stringify(sessionIds);
        document.getElementById('bulk-selected-attendees').value = JSON.stringify(attendeeIds);
    }

    function updateCopyOperationState() {
        const selectedSessions = document.querySelectorAll('.session-checkbox:checked');
        const count = selectedSessions.length;

        const copyBtn = document.getElementById('copy-attendance-btn');
        const summary = document.getElementById('copy-target-summary');

        if (count > 0) {
            copyBtn.disabled = false;
            summary.textContent = `${count} target sessions selected`;
            summary.className = 'text-success';
        } else {
            copyBtn.disabled = true;
            summary.textContent = 'Select sessions above';
            summary.className = 'text-muted';
        }

        // Update hidden form field
        const sessionIds = Array.from(selectedSessions).map(cb => cb.value);
        document.getElementById('copy-target-sessions').value = JSON.stringify(sessionIds);
    }

    // Confirmation functions
    function confirmCrossSessionBulk() {
        const sessionCount = document.querySelectorAll('.session-checkbox:checked').length;
        const attendeeCount = document.querySelectorAll('.attendee-checkbox:checked').length;
        const status = document.querySelector('select[name="attendance_status"]').value;

        const statusText = status.replace('_', ' ');

        return confirm(`Are you sure you want to mark ${attendeeCount} attendees as "${statusText}" across ${sessionCount} sessions?\n\nThis will create or update ${sessionCount * attendeeCount} attendance records.`);
    }

    function confirmCopyAttendance() {
        const sourceSession = document.querySelector('select[name="source_session"] option:checked').textContent;
        const targetCount = document.querySelectorAll('.session-checkbox:checked').length;
        const copyMode = document.querySelector('select[name="copy_mode"]').value;

        const modeText = copyMode === 'attended_only' ? 'attended attendees only' : 'all attendees';

        return confirm(`Are you sure you want to copy ${modeText} from "${sourceSession}" to ${targetCount} target sessions?\n\nThis may create many new attendance records.`);
    }

    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
        updateSessionSelection();
        updateAttendeeSelection();
    });
    </script>

<?php include 'includes/footer.php'; ?>
