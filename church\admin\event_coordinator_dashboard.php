<?php
session_start();

// Include route protection (this will handle login check and role verification)
require_once 'includes/route_protection.php';

// Protect this page - Event Coordinators and <PERSON> Admins only
protectEventCoordinatorRoute();

// Include the configuration file and RBAC system
require_once '../config.php';
require_once 'includes/rbac_system.php';

// Initialize RBAC system
$rbac = new RBACSystem($pdo);

// Check if RBAC system is initialized
if (!$rbac->isInitialized()) {
    header("Location: initialize_rbac_database.php");
    exit();
}

// Require Event Coordinator role (or higher)
if (!$rbac->hasRole('event_coordinator') && !$rbac->hasRole('super_admin')) {
    header("Location: access_denied.php");
    exit();
}

// Log dashboard access
$rbac->logDashboardAccess('event_coordinator_dashboard.php');

$message = '';
$error = '';

// Get assigned events for current user
$assigned_events = $rbac->getAssignedEvents();

// Get detailed event information with session and attendance stats
$event_details = [];
foreach ($assigned_events as $event) {
    // Get sessions for this event
    $stmt = $pdo->prepare("
        SELECT 
            es.*,
            COUNT(sa.id) as total_registered,
            COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) as total_attended,
            COUNT(CASE WHEN sa.attendance_status = 'no_show' THEN 1 END) as total_no_show,
            ROUND((COUNT(CASE WHEN sa.attendance_status = 'attended' THEN 1 END) / NULLIF(COUNT(sa.id), 0)) * 100, 1) as attendance_rate,
            CASE 
                WHEN es.start_datetime > NOW() THEN 'upcoming'
                WHEN es.start_datetime <= NOW() AND es.end_datetime >= NOW() THEN 'active'
                ELSE 'completed'
            END as session_status
        FROM event_sessions es
        LEFT JOIN session_attendance sa ON es.id = sa.session_id
        WHERE es.event_id = ? AND es.status = 'active'
        GROUP BY es.id
        ORDER BY es.start_datetime ASC
    ");
    $stmt->execute([$event['id']]);
    $sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Get overall event attendance stats
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_rsvps,
            COUNT(CASE WHEN actually_attended = 1 THEN 1 END) as event_attended,
            COUNT(CASE WHEN actually_attended = 0 THEN 1 END) as event_not_attended,
            COUNT(CASE WHEN actually_attended IS NULL THEN 1 END) as event_not_marked
        FROM (
            SELECT actually_attended FROM event_rsvps WHERE event_id = ? AND status = 'attending'
            UNION ALL
            SELECT actually_attended FROM event_rsvps_guests WHERE event_id = ? AND status = 'attending'
        ) combined_rsvps
    ");
    $stmt->execute([$event['id'], $event['id']]);
    $event_stats = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Calculate session totals
    $total_session_registered = array_sum(array_column($sessions, 'total_registered'));
    $total_session_attended = array_sum(array_column($sessions, 'total_attended'));
    
    $event_details[] = [
        'event' => $event,
        'sessions' => $sessions,
        'event_stats' => $event_stats,
        'session_totals' => [
            'total_registered' => $total_session_registered,
            'total_attended' => $total_session_attended,
            'attendance_rate' => $total_session_registered > 0 ? round(($total_session_attended / $total_session_registered) * 100, 1) : 0
        ]
    ];
}

// Page title and header info
$page_title = 'Event Coordinator Dashboard';
$page_header = 'Event Coordinator Dashboard';
$page_description = 'Coordinate multiple sessions within your assigned events';

// Include header
include 'includes/header.php';
?>

<style>
.coordinator-header {
    background: linear-gradient(135deg, #fd7e14 0%, #ffc107 100%);
    color: white;
    border-radius: 10px;
}
.event-card {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
    border-radius: 10px;
    margin-bottom: 30px;
}
.event-card.active-event {
    border-color: #fd7e14;
    background-color: #fff8f0;
}
.session-mini-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    margin: 5px 0;
    transition: all 0.2s ease;
}
.session-mini-card:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}
.session-mini-card.active {
    background-color: #d4edda;
    border-color: #28a745;
}
.session-mini-card.upcoming {
    background-color: #fff3cd;
    border-color: #ffc107;
}
.session-mini-card.completed {
    background-color: #f8f9fa;
    border-color: #6c757d;
}
.stats-overview {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}
.quick-action-btn {
    border-radius: 8px;
    padding: 10px;
    margin: 5px;
    transition: all 0.3s ease;
}
.quick-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}
</style>

<!-- Header -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card coordinator-header">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="text-white mb-1">
                            <i class="bi bi-calendar-event"></i> Event Coordinator Dashboard
                        </h2>
                        <p class="text-white-50 mb-0">Coordinate multiple sessions within your assigned events</p>
                    </div>
                    <div class="text-end">
                        <div class="btn-group">
                            <button class="btn btn-outline-light" onclick="refreshDashboard()">
                                <i class="bi bi-arrow-clockwise"></i> Refresh
                            </button>
                            <?php if ($rbac->hasRole('super_admin')): ?>
                                <a href="super_admin_dashboard.php" class="btn btn-outline-light">
                                    <i class="bi bi-speedometer2"></i> Super Admin
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success/Error Messages -->
<?php if ($message): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle"></i> <?php echo htmlspecialchars($message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if ($error): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- No Events Assigned -->
<?php if (empty($event_details)): ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="bi bi-calendar-x text-muted" style="font-size: 4rem;"></i>
                    <h4 class="mt-3 text-muted">No Events Assigned</h4>
                    <p class="lead text-muted">You don't have any events assigned to coordinate yet.</p>
                    <div class="mt-4">
                        <p class="text-muted">Contact your administrator to get assigned to events as a coordinator.</p>
                        <?php if ($rbac->hasRole('super_admin')): ?>
                            <a href="setup_rbac_system.php" class="btn btn-primary">
                                <i class="bi bi-person-plus"></i> Assign Events
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>

<!-- Assigned Events -->
<?php foreach ($event_details as $detail):
    $event = $detail['event'];
    $sessions = $detail['sessions'];
    $event_stats = $detail['event_stats'];
    $session_totals = $detail['session_totals'];

    // Determine if event has active sessions
    $has_active_sessions = !empty(array_filter($sessions, function($s) { return $s['session_status'] === 'active'; }));
?>

<div class="row">
    <div class="col-md-12">
        <div class="card event-card <?php echo $has_active_sessions ? 'active-event' : ''; ?>">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-0">
                            <i class="bi bi-calendar-event"></i>
                            <?php echo htmlspecialchars($event['title']); ?>
                        </h4>
                        <small class="text-muted">
                            <?php echo date('F j, Y g:i A', strtotime($event['event_date'])); ?>
                            <?php if ($event['location']): ?>
                                • <i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($event['location']); ?>
                            <?php endif; ?>
                        </small>
                    </div>
                    <div class="text-end">
                        <?php if ($has_active_sessions): ?>
                            <span class="badge bg-success fs-6">
                                <i class="bi bi-broadcast"></i> Live Sessions
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <!-- Event Overview Stats -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card stats-overview">
                            <div class="card-body py-3">
                                <h6 class="text-white mb-2">
                                    <i class="bi bi-graph-up"></i> Event Overview
                                </h6>
                                <div class="row text-center">
                                    <div class="col-md-2">
                                        <h5 class="text-white mb-0"><?php echo count($sessions); ?></h5>
                                        <small class="text-white-50">Sessions</small>
                                    </div>
                                    <div class="col-md-2">
                                        <h5 class="text-white mb-0"><?php echo $event_stats['total_rsvps']; ?></h5>
                                        <small class="text-white-50">Event RSVPs</small>
                                    </div>
                                    <div class="col-md-2">
                                        <h5 class="text-white mb-0"><?php echo $session_totals['total_registered']; ?></h5>
                                        <small class="text-white-50">Session Registrations</small>
                                    </div>
                                    <div class="col-md-2">
                                        <h5 class="text-white mb-0"><?php echo $session_totals['total_attended']; ?></h5>
                                        <small class="text-white-50">Session Attended</small>
                                    </div>
                                    <div class="col-md-2">
                                        <h5 class="text-white mb-0"><?php echo $session_totals['attendance_rate']; ?>%</h5>
                                        <small class="text-white-50">Session Rate</small>
                                    </div>
                                    <div class="col-md-2">
                                        <h5 class="text-white mb-0"><?php echo $event_stats['event_attended']; ?></h5>
                                        <small class="text-white-50">Event Attended</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h6><i class="bi bi-lightning"></i> Quick Actions</h6>
                        <div class="d-flex flex-wrap">
                            <a href="event_attendance_detail.php?event_id=<?php echo $event['id']; ?>"
                               class="btn btn-primary quick-action-btn">
                                <i class="bi bi-list-check"></i> Event Attendance
                            </a>
                            <a href="multi_session_dashboard.php?event_id=<?php echo $event['id']; ?>"
                               class="btn btn-success quick-action-btn">
                                <i class="bi bi-speedometer2"></i> Multi-Session Dashboard
                            </a>
                            <a href="cross_session_attendance.php?event_id=<?php echo $event['id']; ?>"
                               class="btn btn-warning quick-action-btn">
                                <i class="bi bi-diagram-3"></i> Cross-Session Operations
                            </a>
                            <a href="smart_attendance_rules.php?event_id=<?php echo $event['id']; ?>"
                               class="btn btn-info quick-action-btn">
                                <i class="bi bi-lightbulb"></i> Smart Rules
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Sessions Overview -->
                <div class="row">
                    <div class="col-md-12">
                        <h6><i class="bi bi-diagram-3"></i> Sessions Overview</h6>

                        <?php if (empty($sessions)): ?>
                            <div class="text-center text-muted py-3">
                                <i class="bi bi-calendar-x fs-3 d-block mb-2"></i>
                                <p>No sessions configured for this event.</p>
                                <a href="event_sessions.php?event_id=<?php echo $event['id']; ?>" class="btn btn-primary">
                                    <i class="bi bi-plus"></i> Add Sessions
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="row">
                                <?php foreach ($sessions as $session): ?>
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="session-mini-card <?php echo $session['session_status']; ?>"
                                             onclick="openSessionDetail(<?php echo $session['id']; ?>)">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <h6 class="mb-0"><?php echo htmlspecialchars($session['session_title']); ?></h6>
                                                <span class="badge bg-<?php
                                                    echo $session['session_status'] === 'active' ? 'success' :
                                                        ($session['session_status'] === 'upcoming' ? 'warning' : 'secondary');
                                                ?>">
                                                    <?php echo ucfirst($session['session_status']); ?>
                                                </span>
                                            </div>

                                            <div class="mb-2">
                                                <small class="text-muted">
                                                    <i class="bi bi-clock"></i> <?php echo date('M j, g:i A', strtotime($session['start_datetime'])); ?>
                                                    <?php if ($session['location']): ?>
                                                        <br><i class="bi bi-geo-alt"></i> <?php echo htmlspecialchars($session['location']); ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>

                                            <div class="row text-center">
                                                <div class="col-4">
                                                    <strong class="text-success"><?php echo $session['total_attended']; ?></strong>
                                                    <br><small class="text-muted">Attended</small>
                                                </div>
                                                <div class="col-4">
                                                    <strong class="text-primary"><?php echo $session['total_registered']; ?></strong>
                                                    <br><small class="text-muted">Registered</small>
                                                </div>
                                                <div class="col-4">
                                                    <strong class="text-info"><?php echo $session['attendance_rate'] ?? 0; ?>%</strong>
                                                    <br><small class="text-muted">Rate</small>
                                                </div>
                                            </div>

                                            <div class="mt-2">
                                                <div class="progress" style="height: 4px;">
                                                    <div class="progress-bar bg-success"
                                                         style="width: <?php echo $session['attendance_rate'] ?? 0; ?>%"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php endforeach; ?>
<?php endif; ?>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

<script>
// Event coordinator dashboard functionality
function refreshDashboard() {
    location.reload();
}

function openSessionDetail(sessionId) {
    window.location.href = `session_attendance.php?session_id=${sessionId}`;
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Add hover effects for session cards
    document.querySelectorAll('.session-mini-card').forEach(card => {
        card.style.cursor = 'pointer';
    });

    // Auto-refresh every 3 minutes for events with active sessions
    const activeEvents = document.querySelectorAll('.event-card.active-event');
    if (activeEvents.length > 0) {
        setInterval(function() {
            location.reload();
        }, 180000); // 3 minutes
    }

    // Add tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + M to go to multi-session dashboard for first event
    if ((e.ctrlKey || e.metaKey) && e.key === 'm') {
        e.preventDefault();
        const firstEventLink = document.querySelector('a[href*="multi_session_dashboard.php"]');
        if (firstEventLink) {
            window.location.href = firstEventLink.href;
        }
    }

    // Ctrl/Cmd + C to go to cross-session operations for first event
    if ((e.ctrlKey || e.metaKey) && e.key === 'c') {
        e.preventDefault();
        const firstCrossSessionLink = document.querySelector('a[href*="cross_session_attendance.php"]');
        if (firstCrossSessionLink) {
            window.location.href = firstCrossSessionLink.href;
        }
    }
});
</script>

<?php include 'includes/footer.php'; ?>
